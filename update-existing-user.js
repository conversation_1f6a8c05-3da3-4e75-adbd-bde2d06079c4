// Script para actualizar usuario existente con fechas de membresía - MACLEAN
import { MongoClient } from 'mongodb'

const uri = process.env.MONGODB_URI || 'mongodb+srv://macleanjhon8:<EMAIL>/germayori-signals?retryWrites=true&w=majority&appName=SenalesMaclean'

async function updateExistingUser() {
  let client
  
  try {
    console.log('🚀 Conectando a MongoDB...')
    client = new MongoClient(uri)
    await client.connect()
    
    const db = client.db('germayori-signals')
    const users = db.collection('users')
    
    console.log('🔍 Actualizando todos los usuarios existentes...')

    const allUsers = await users.find({}).toArray()

    if (allUsers.length === 0) {
      console.log('❌ No se encontraron usuarios')
      return
    }

    console.log(`✅ Se encontraron ${allUsers.length} usuarios para actualizar`)

    // Calcular fechas de membresía
    const now = new Date()
    const membershipStart = new Date(now)
    const membershipEnd = new Date(now)
    membershipEnd.setDate(membershipEnd.getDate() + 30) // 30 días de acceso

    console.log('📅 Fechas de membresía:')
    console.log('   Inicio:', membershipStart.toLocaleDateString())
    console.log('   Fin:', membershipEnd.toLocaleDateString())
    console.log('')

    for (let user of allUsers) {
      console.log(`🔄 Actualizando usuario: ${user.email}`)

      const result = await users.updateOne(
        { _id: user._id },
        {
          $set: {
            isPaid: true,
            hasAccess: true,
            isActive: true,
            paidAt: now,
            // Fechas de entrada y salida de la tarjeta/membresía
            membershipStartDate: membershipStart,
            membershipEndDate: membershipEnd,
            membershipExpirationDate: membershipEnd,
            daysRemaining: 30,
            lastAccessDate: now,
            membershipStatus: 'ACTIVE',
            updatedAt: now
          }
        }
      )

      if (result.modifiedCount > 0) {
        console.log(`   ✅ ${user.email} actualizado exitosamente`)
      } else {
        console.log(`   ⚠️ ${user.email} no se pudo actualizar`)
      }
    }

    console.log('\n🎉 Proceso completado!')
    console.log('💰 Todos los usuarios: PAGADOS y ACTIVOS')
    console.log('📅 Membresía válida por 30 días')
    console.log('🔑 Acceso completo habilitado')

    // Mostrar usuarios actualizados
    const updatedUsers = await users.find({}).toArray()
    console.log('\n📋 Usuarios actualizados:')
    updatedUsers.forEach((user, index) => {
      console.log(`\n   👤 Usuario ${index + 1}:`)
      console.log('      Email:', user.email)
      console.log('      Nombre:', user.name)
      console.log('      Estado:', user.isActive ? 'ACTIVO' : 'INACTIVO')
      console.log('      Pagado:', user.isPaid ? 'SÍ' : 'NO')
      console.log('      Acceso:', user.hasAccess ? 'SÍ' : 'NO')
      console.log('      Días restantes:', user.daysRemaining)
      console.log('      Inicio membresía:', user.membershipStartDate?.toLocaleDateString())
      console.log('      Fin membresía:', user.membershipEndDate?.toLocaleDateString())
    })
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    if (client) {
      await client.close()
      console.log('🔌 Conexión cerrada')
    }
  }
}

updateExistingUser()
