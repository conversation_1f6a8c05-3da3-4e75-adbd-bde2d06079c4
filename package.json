{"name": "germayori-trading-signals", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "validate-production": "node scripts/validate-production.mjs", "pre-deploy": "npm run validate-production && npm run build", "health-check": "curl -f http://localhost:3000/api/health || exit 1"}, "dependencies": {"@tailwindcss/forms": "^0.5.7", "bcryptjs": "^2.4.3", "formidable": "^3.5.4", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "next": "14.0.4", "qrcode": "^1.5.3", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}, "keywords": ["trading", "signals", "yappy", "payments", "nextjs"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "ISC"}