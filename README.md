# 🚀 Germayori Trading Signals Platform

Plataforma de señales de trading premium con sistema de pagos Yappy y acceso exclusivo a grupo de WhatsApp.

## 💰 Características

- ✅ **Sistema de pagos Yappy** - QR real para $25 USD
- ✅ **Registro y autenticación** - Control de acceso
- ✅ **Expiración automática** - 30 días de acceso
- ✅ **Área privada de señales** - Solo para usuarios pagados
- ✅ **Integración WhatsApp** - Acceso automático al grupo VIP
- ✅ **Responsive design** - Optimizado para móviles
- ✅ **Next.js** - Listo para producción

## 🛠️ Tecnologías

- **Next.js 14** - Framework React
- **Tailwind CSS** - Estilos
- **MongoDB** - Base de datos
- **QRCode** - Generación de códigos QR
- **Vercel** - Deploy (recomendado)

## 🚀 Deploy a Producción

### Opción 1: Vercel (Recomendado - GRATIS)

1. **Sube el proyecto a GitHub:**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin https://github.com/tu-usuario/germayori-signals.git
   git push -u origin main
   ```

2. **Deploy en Vercel:**
   - Ve a [vercel.com](https://vercel.com)
   - Conecta tu cuenta de GitHub
   - Selecciona el repositorio
   - Deploy automático ✅

3. **Configurar variables de entorno en Vercel:**
   - `MONGODB_URI`: `mongodb+srv://macleanjhon8:<EMAIL>/?retryWrites=true&w=majority&appName=SenalesMaclean`
   - `SESSION_SECRET`: `germayori-trading-secret-2024`

### Opción 2: Netlify (Alternativa GRATIS)

1. **Sube a GitHub** (mismo proceso)
2. **Deploy en Netlify:**
   - Ve a [netlify.com](https://netlify.com)
   - Conecta GitHub
   - Selecciona repositorio
   - Build command: `npm run build`
   - Publish directory: `.next`

## 🔧 Desarrollo Local

```bash
# Instalar dependencias
npm install

# Ejecutar en desarrollo
npm run dev

# Construir para producción
npm run build

# Ejecutar en producción
npm start
```

## 📊 Base de Datos

La base de datos MongoDB ya está configurada con:
- **Usuarios** - Registro y suscripciones
- **Señales** - Señales de trading premium
- **Pagos** - Historial de transacciones
- **Configuración** - Settings del sistema

## 💳 Flujo de Pago

1. **Usuario ve landing page**
2. **Escanea QR de Yappy** ($25 USD)
3. **Completa registro**
4. **Confirma pago**
5. **Acceso activado por 30 días**
6. **Redirigido a WhatsApp automáticamente**
7. **Acceso a área de señales premium**

## 📱 WhatsApp Integration

- **Grupo VIP:** https://chat.whatsapp.com/DJSyPHE4h06CjBMiu8LkM4
- **Acceso automático** después del pago
- **Notificaciones en tiempo real**

## 🎯 URLs de Producción

Una vez desplegado tendrás:
- **Landing page:** `https://tu-dominio.vercel.app`
- **Login:** `https://tu-dominio.vercel.app/login`
- **Señales:** `https://tu-dominio.vercel.app/signals`
- **QR API:** `https://tu-dominio.vercel.app/api/qr-yappy`

## 💰 Monetización

- **$25 USD por usuario** cada 30 días
- **Renovación automática** requerida
- **Acceso inmediato** al grupo de WhatsApp
- **Escalable** para miles de usuarios

## 🔒 Seguridad

- ✅ Autenticación por sesiones
- ✅ Validación de expiración
- ✅ Acceso controlado a áreas premium
- ✅ Datos encriptados en MongoDB

## 📞 Soporte

Para soporte técnico o modificaciones:
- **WhatsApp:** [Enlace del grupo](https://chat.whatsapp.com/DJSyPHE4h06CjBMiu8LkM4)
- **Email:** <EMAIL>

---

**🚀 ¡Tu plataforma está lista para generar ingresos reales!**
#   S e n a l e s - M a c l e a n 
 
 #   S e n a l e s - M a c l e a n 
 
 