// Script para restaurar acceso de usuario - MACLEAN
import { MongoClient } from 'mongodb'

const uri = 'mongodb+srv://macleanjhon8:<EMAIL>/?retryWrites=true&w=majority&appName=SenalesMaclean'

async function restoreUserAccess(email, name, phone) {
  let client
  
  try {
    console.log('🚀 Conectando a MongoDB...')
    client = new MongoClient(uri)
    await client.connect()
    
    const db = client.db()
    const users = db.collection('users')
    
    // Verificar si el usuario ya existe
    const existingUser = await users.findOne({ email: email.toLowerCase() })
    
    if (existingUser) {
      console.log('👤 Usuario encontrado, actualizando acceso...')
      
      // Calcular fechas de membresía activa
      const now = new Date()
      const membershipEnd = new Date(now)
      membershipEnd.setDate(membershipEnd.getDate() + 30) // 30 días de acceso
      
      const result = await users.updateOne(
        { email: email.toLowerCase() },
        {
          $set: {
            isPaid: true,
            hasAccess: true,
            isActive: true,
            paidAt: now,
            membershipStartDate: now,
            membershipEndDate: membershipEnd,
            membershipExpirationDate: membershipEnd,
            daysRemaining: 30,
            lastAccessDate: now,
            membershipStatus: 'ACTIVE'
          }
        }
      )
      
      console.log('✅ Acceso restaurado exitosamente!')
      console.log('📅 Membresía válida hasta:', membershipEnd.toLocaleDateString())
      
    } else {
      console.log('👤 Usuario no encontrado, creando nuevo usuario con acceso completo...')
      
      // Crear nuevo usuario con acceso completo
      const now = new Date()
      const membershipEnd = new Date(now)
      membershipEnd.setDate(membershipEnd.getDate() + 30)
      
      const newUser = {
        name: name,
        email: email.toLowerCase(),
        phone: phone,
        password: 'temp123', // Contraseña temporal
        createdAt: now,
        registeredAt: now,
        isPaid: true,
        hasAccess: true,
        isActive: true,
        paidAt: now,
        membershipStartDate: now,
        membershipEndDate: membershipEnd,
        membershipExpirationDate: membershipEnd,
        daysRemaining: 30,
        lastAccessDate: now,
        membershipStatus: 'ACTIVE'
      }
      
      const result = await users.insertOne(newUser)
      console.log('✅ Usuario creado con acceso completo!')
      console.log('🆔 ID del usuario:', result.insertedId.toString())
      console.log('📅 Membresía válida hasta:', membershipEnd.toLocaleDateString())
      console.log('🔑 Contraseña temporal: temp123')
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    if (client) {
      await client.close()
      console.log('\n🔌 Conexión cerrada')
    }
  }
}

// Usar el script
const email = process.argv[2]
const name = process.argv[3]
const phone = process.argv[4]

if (!email || !name || !phone) {
  console.log('❌ Uso: node restore-access.mjs <email> <nombre> <telefono>')
  console.log('📝 Ejemplo: node restore-access.mjs <EMAIL> "Juan Pérez" "+507 1234-5678"')
} else {
  restoreUserAccess(email, name, phone)
}
