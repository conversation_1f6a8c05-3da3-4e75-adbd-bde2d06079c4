import mongoose from 'mongoose'

const SignalSchema = new mongoose.Schema({
  pair: {
    type: String,
    required: true,
    uppercase: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['BUY', 'SELL'],
    required: true
  },
  entryPrice: {
    type: Number,
    required: true
  },
  stopLoss: {
    type: Number,
    required: true
  },
  takeProfit: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'hit_tp', 'hit_sl', 'cancelled'],
    default: 'active'
  },
  confidence: {
    type: Number,
    min: 1,
    max: 5,
    default: 3
  },
  timeframe: {
    type: String,
    enum: ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
    default: '1h'
  },
  analysis: {
    type: String,
    trim: true
  },
  result: {
    pips: Number,
    percentage: Number,
    profit: Number
  },
  createdBy: {
    type: String,
    default: 'GERMAYORI'
  },
  isVIP: {
    type: Boolean,
    default: false
  },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 horas
  }
}, {
  timestamps: true
})

// Índices para optimizar consultas
SignalSchema.index({ createdAt: -1 })
SignalSchema.index({ status: 1 })
SignalSchema.index({ isVIP: 1 })
SignalSchema.index({ pair: 1 })

export default mongoose.models.Signal || mongoose.model('Signal', SignalSchema)
