import mongoose from 'mongoose'

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  phone: {
    type: String,
    required: true,
    trim: true
  },
  country: {
    type: String,
    required: true,
    trim: true
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'verified'],
    default: 'pending'
  },
  paymentAmount: {
    type: Number,
    default: 25
  },
  paymentDate: {
    type: Date
  },
  accessGranted: {
    type: Boolean,
    default: false
  },
  whatsappJoined: {
    type: Boolean,
    default: false
  },
  registrationDate: {
    type: Date,
    default: Date.now
  },
  lastLogin: {
    type: Date
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
})

// Índices para optimizar consultas
UserSchema.index({ email: 1 })
UserSchema.index({ paymentStatus: 1 })
UserSchema.index({ accessGranted: 1 })

export default mongoose.models.User || mongoose.model('User', UserSchema)
