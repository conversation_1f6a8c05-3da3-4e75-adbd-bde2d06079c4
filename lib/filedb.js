// SISTEMA DE BASE DE DATOS TEMPORAL CON ARCHIVOS JSON - MACLEAN
import fs from 'fs'
import path from 'path'

const DB_DIR = path.join(process.cwd(), 'data')
const USERS_FILE = path.join(DB_DIR, 'users.json')
const SIGNALS_FILE = path.join(DB_DIR, 'signals.json')

// Crear directorio si no existe
if (!fs.existsSync(DB_DIR)) {
  fs.mkdirSync(DB_DIR, { recursive: true })
}

// Inicializar archivos si no existen
if (!fs.existsSync(USERS_FILE)) {
  fs.writeFileSync(USERS_FILE, JSON.stringify([]))
}

if (!fs.existsSync(SIGNALS_FILE)) {
  fs.writeFileSync(SIGNALS_FILE, JSON.stringify([]))
}

// Funciones para usuarios
export async function createUser(userData) {
  try {
    const users = JSON.parse(fs.readFileSync(USERS_FILE, 'utf8'))
    
    // Verificar si el email ya existe
    const existingUser = users.find(user => user.email === userData.email.toLowerCase())
    if (existingUser) {
      return { success: false, error: 'El email ya está registrado' }
    }
    
    const newUser = {
      _id: Date.now().toString(),
      ...userData,
      email: userData.email.toLowerCase(),
      createdAt: new Date(),
      isPaid: false,
      hasAccess: false
    }
    
    users.push(newUser)
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2))
    
    return { success: true, userId: newUser._id }
  } catch (error) {
    console.error('Error creating user:', error)
    return { success: false, error: error.message }
  }
}

export async function getAllUsers() {
  try {
    const users = JSON.parse(fs.readFileSync(USERS_FILE, 'utf8'))
    return { success: true, users }
  } catch (error) {
    console.error('Error getting users:', error)
    return { success: false, error: error.message }
  }
}

export async function getUserById(userId) {
  try {
    const users = JSON.parse(fs.readFileSync(USERS_FILE, 'utf8'))
    const user = users.find(u => u._id === userId)
    return { success: true, user }
  } catch (error) {
    console.error('Error getting user:', error)
    return { success: false, error: error.message }
  }
}

export async function markUserAsPaid(userId) {
  try {
    const users = JSON.parse(fs.readFileSync(USERS_FILE, 'utf8'))
    const userIndex = users.findIndex(u => u._id === userId)
    
    if (userIndex === -1) {
      return { success: false, error: 'Usuario no encontrado' }
    }
    
    users[userIndex].isPaid = true
    users[userIndex].hasAccess = true
    users[userIndex].paidAt = new Date()
    
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2))
    
    return { success: true, user: users[userIndex] }
  } catch (error) {
    console.error('Error marking user as paid:', error)
    return { success: false, error: error.message }
  }
}

// Funciones para señales
export async function createSignal(signalData) {
  try {
    const signals = JSON.parse(fs.readFileSync(SIGNALS_FILE, 'utf8'))
    
    const newSignal = {
      _id: Date.now().toString(),
      ...signalData,
      createdAt: new Date(),
      isActive: true
    }
    
    signals.push(newSignal)
    fs.writeFileSync(SIGNALS_FILE, JSON.stringify(signals, null, 2))
    
    return { success: true, signalId: newSignal._id }
  } catch (error) {
    console.error('Error creating signal:', error)
    return { success: false, error: error.message }
  }
}

export async function getAllSignals() {
  try {
    const signals = JSON.parse(fs.readFileSync(SIGNALS_FILE, 'utf8'))
    // Ordenar por fecha más reciente
    signals.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    return { success: true, signals }
  } catch (error) {
    console.error('Error getting signals:', error)
    return { success: false, error: error.message }
  }
}

export async function updateSignal(signalId, updateData) {
  try {
    const signals = JSON.parse(fs.readFileSync(SIGNALS_FILE, 'utf8'))
    const signalIndex = signals.findIndex(s => s._id === signalId)
    
    if (signalIndex === -1) {
      return { success: false, error: 'Señal no encontrada' }
    }
    
    signals[signalIndex] = { ...signals[signalIndex], ...updateData, updatedAt: new Date() }
    fs.writeFileSync(SIGNALS_FILE, JSON.stringify(signals, null, 2))
    
    return { success: true, signal: signals[signalIndex] }
  } catch (error) {
    console.error('Error updating signal:', error)
    return { success: false, error: error.message }
  }
}

export async function deleteSignal(signalId) {
  try {
    const signals = JSON.parse(fs.readFileSync(SIGNALS_FILE, 'utf8'))
    const initialLength = signals.length
    const filteredSignals = signals.filter(s => s._id !== signalId)
    
    fs.writeFileSync(SIGNALS_FILE, JSON.stringify(filteredSignals, null, 2))
    
    return { success: true, deleted: initialLength - filteredSignals.length }
  } catch (error) {
    console.error('Error deleting signal:', error)
    return { success: false, error: error.message }
  }
}
