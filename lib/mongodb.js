import { MongoClient, ObjectId } from 'mongodb'

// VALIDACIÓN CRÍTICA - NO PUEDE FALLAR EN PRODUCCIÓN
if (!process.env.MONGODB_URI) {
  throw new Error('❌ CRITICAL ERROR: MONGODB_URI environment variable is required')
}

const uri = process.env.MONGODB_URI
const DATABASE_NAME = 'germayori-signals' // FIJO - NO PUEDE CAMBIAR

// Validar que la URI contenga la base de datos correcta
if (!uri.includes('senalesmaclean.wckyrop.mongodb.net')) {
  throw new Error('❌ CRITICAL ERROR: Invalid MongoDB URI - must connect to senalesmaclean cluster')
}

const options = {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
}

// FUNCIÓN SEGURA PARA OBTENER BASE DE DATOS
async function getDatabase() {
  try {
    const client = await clientPromise
    const db = client.db(DATABASE_NAME)

    // VALIDAR CONEXIÓN EN CADA USO
    await db.admin().ping()
    return db
  } catch (error) {
    console.error('❌ CRITICAL DATABASE ERROR:', error)
    throw new Error(`Database connection failed: ${error.message}`)
  }
}

let client
let clientPromise

if (process.env.NODE_ENV === 'development') {
  // En desarrollo, usa una variable global para preservar el valor
  // a través de module reloads causados por HMR (Hot Module Replacement).
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri, options)
    global._mongoClientPromise = client.connect()
  }
  clientPromise = global._mongoClientPromise
} else {
  // En producción, es mejor no usar una variable global.
  client = new MongoClient(uri, options)
  clientPromise = client.connect()
}

export default clientPromise
export { getDatabase }

// FUNCIONES PARA USUARIOS REALES - MACLEAN
export async function createUser(userData) {
  try {
    const db = await getDatabase()
    const users = db.collection('users')

    // Calcular fecha de vencimiento (30 días desde el registro)
    const registrationDate = new Date()
    const expirationDate = new Date(registrationDate)
    expirationDate.setDate(expirationDate.getDate() + 30)

    const user = {
      ...userData,
      createdAt: registrationDate,
      registeredAt: registrationDate,
      isPaid: false,
      hasAccess: false,
      // Fechas de tarjeta/membresía
      membershipStartDate: null,
      membershipEndDate: null,
      membershipExpirationDate: expirationDate,
      isActive: false,
      daysRemaining: 30
    }

    const result = await users.insertOne(user)
    return { success: true, userId: result.insertedId }
  } catch (error) {
    console.error('Error creating user:', error)
    return { success: false, error: error.message }
  }
}

export async function markUserAsPaid(userId) {
  try {
    const client = await clientPromise
    const db = client.db('germayori-signals')
    const users = db.collection('users')

    // Calcular fechas de membresía activa
    const paymentDate = new Date()
    const membershipStart = new Date(paymentDate)
    const membershipEnd = new Date(paymentDate)
    membershipEnd.setDate(membershipEnd.getDate() + 30) // 30 días de acceso

    // Convertir userId a ObjectId si es necesario
    let query
    if (typeof userId === 'string' && userId.length === 24) {
      try {
        query = { _id: new ObjectId(userId) }
      } catch {
        query = { _id: userId }
      }
    } else {
      query = { _id: userId }
    }

    const result = await users.updateOne(
      query,
      {
        $set: {
          isPaid: true,
          hasAccess: true,
          isActive: true,
          paidAt: paymentDate,
          // Fechas de entrada y salida de la tarjeta/membresía
          membershipStartDate: membershipStart,
          membershipEndDate: membershipEnd,
          membershipExpirationDate: membershipEnd,
          daysRemaining: 30,
          lastAccessDate: paymentDate
        }
      }
    )

    return { success: true, modified: result.modifiedCount }
  } catch (error) {
    console.error('Error marking user as paid:', error)
    return { success: false, error: error.message }
  }
}

export async function getUserById(userId) {
  try {
    const client = await clientPromise
    const db = client.db('germayori-signals')
    const users = db.collection('users')

    // Convertir userId a ObjectId si es necesario
    let query
    if (typeof userId === 'string' && userId.length === 24) {
      try {
        query = { _id: new ObjectId(userId) }
      } catch {
        query = { _id: userId }
      }
    } else {
      query = { _id: userId }
    }

    const user = await users.findOne(query)

    // Verificar y actualizar estado de membresía si es necesario
    if (user && user.membershipEndDate) {
      const now = new Date()
      const endDate = new Date(user.membershipEndDate)
      const daysLeft = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24))

      if (endDate < now && user.isActive) {
        // Membresía vencida
        await users.updateOne(
          { _id: userId },
          {
            $set: {
              isActive: false,
              hasAccess: false,
              daysRemaining: 0,
              membershipStatus: 'EXPIRED'
            }
          }
        )
        user.isActive = false
        user.hasAccess = false
        user.daysRemaining = 0
        user.membershipStatus = 'EXPIRED'
      } else if (user.isActive) {
        // Actualizar días restantes
        user.daysRemaining = Math.max(0, daysLeft)
        await users.updateOne(
          { _id: userId },
          { $set: { daysRemaining: user.daysRemaining } }
        )
      }
    }

    return { success: true, user }
  } catch (error) {
    console.error('Error getting user:', error)
    return { success: false, error: error.message }
  }
}

export async function getAllUsers() {
  try {
    const client = await clientPromise
    const db = client.db('germayori-signals')
    const users = db.collection('users')

    const allUsers = await users.find({}).toArray()

    // Actualizar estado de membresías vencidas
    const now = new Date()
    for (let user of allUsers) {
      if (user.membershipEndDate && new Date(user.membershipEndDate) < now && user.isActive) {
        await users.updateOne(
          { _id: user._id },
          {
            $set: {
              isActive: false,
              hasAccess: false,
              daysRemaining: 0,
              membershipStatus: 'EXPIRED'
            }
          }
        )
        user.isActive = false
        user.hasAccess = false
        user.daysRemaining = 0
        user.membershipStatus = 'EXPIRED'
      } else if (user.membershipEndDate && user.isActive) {
        // Calcular días restantes
        const daysLeft = Math.ceil((new Date(user.membershipEndDate) - now) / (1000 * 60 * 60 * 24))
        if (user.daysRemaining !== daysLeft) {
          await users.updateOne(
            { _id: user._id },
            { $set: { daysRemaining: Math.max(0, daysLeft) } }
          )
          user.daysRemaining = Math.max(0, daysLeft)
        }
      }
    }

    return { success: true, users: allUsers }
  } catch (error) {
    console.error('Error getting all users:', error)
    return { success: false, error: error.message }
  }
}

// FUNCIÓN PARA VERIFICAR ESTADO DE MEMBRESÍA
export async function checkMembershipStatus(email) {
  try {
    const client = await clientPromise
    const db = client.db('germayori-signals')
    const users = db.collection('users')

    const user = await users.findOne({ email: email.toLowerCase() })

    if (!user) {
      return { success: false, error: 'Usuario no encontrado' }
    }

    const now = new Date()
    let membershipInfo = {
      isActive: user.isActive || false,
      isPaid: user.isPaid || false,
      hasAccess: user.hasAccess || false,
      membershipStartDate: user.membershipStartDate,
      membershipEndDate: user.membershipEndDate,
      daysRemaining: 0,
      membershipStatus: 'INACTIVE'
    }

    if (user.membershipEndDate) {
      const endDate = new Date(user.membershipEndDate)
      const daysLeft = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24))

      if (endDate < now) {
        // Membresía vencida
        membershipInfo.membershipStatus = 'EXPIRED'
        membershipInfo.isActive = false
        membershipInfo.hasAccess = false
        membershipInfo.daysRemaining = 0

        // Actualizar en base de datos
        await users.updateOne(
          { _id: user._id },
          {
            $set: {
              isActive: false,
              hasAccess: false,
              daysRemaining: 0,
              membershipStatus: 'EXPIRED'
            }
          }
        )
      } else if (user.isPaid) {
        // Membresía activa
        membershipInfo.membershipStatus = 'ACTIVE'
        membershipInfo.isActive = true
        membershipInfo.hasAccess = true
        membershipInfo.daysRemaining = Math.max(0, daysLeft)

        // Actualizar días restantes
        await users.updateOne(
          { _id: user._id },
          { $set: { daysRemaining: membershipInfo.daysRemaining } }
        )
      }
    }

    return { success: true, membershipInfo, user }
  } catch (error) {
    console.error('Error checking membership status:', error)
    return { success: false, error: error.message }
  }
}



// FUNCIONES PARA SEÑALES DE TRADING - MACLEAN
export async function createSignal(signalData) {
  try {
    const client = await clientPromise
    const db = client.db('germayori-signals')
    const signals = db.collection('signals')

    const signal = {
      ...signalData,
      createdAt: new Date(),
      isActive: signalData.status === 'ACTIVA',
      isClosed: signalData.status === 'CERRADA',
      // Asegurar que siempre tengamos estructura de múltiples TPs
      takeProfit1: signalData.takeProfit1 || signalData.takeProfit,
      takeProfit2: signalData.takeProfit2 || null,
      takeProfit3: signalData.takeProfit3 || null
    }

    const result = await signals.insertOne(signal)
    return { success: true, signalId: result.insertedId }
  } catch (error) {
    console.error('Error creating signal:', error)
    return { success: false, error: error.message }
  }
}

export async function getAllSignals() {
  try {
    const client = await clientPromise
    const db = client.db('germayori-signals')
    const signals = db.collection('signals')

    const allSignals = await signals.find({}).sort({ createdAt: -1 }).toArray()
    return { success: true, signals: allSignals }
  } catch (error) {
    console.error('Error getting signals:', error)
    return { success: false, error: error.message }
  }
}

export async function updateSignal(signalId, updateData) {
  try {
    const client = await clientPromise
    const db = client.db('germayori-signals')
    const signals = db.collection('signals')

    const result = await signals.updateOne(
      { _id: signalId },
      {
        $set: {
          ...updateData,
          updatedAt: new Date(),
          isActive: updateData.status === 'ACTIVA',
          isClosed: updateData.status === 'CERRADA'
        }
      }
    )

    return { success: true, modified: result.modifiedCount }
  } catch (error) {
    console.error('Error updating signal:', error)
    return { success: false, error: error.message }
  }
}

export async function deleteSignal(signalId) {
  try {
    const client = await clientPromise
    const db = client.db('germayori-signals')
    const signals = db.collection('signals')

    const result = await signals.deleteOne({ _id: signalId })
    return { success: true, deleted: result.deletedCount }
  } catch (error) {
    console.error('Error deleting signal:', error)
    return { success: false, error: error.message }
  }
}
