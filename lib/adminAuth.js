// MIDDLEWARE DE SEGURIDAD - SOLO PARA EL DUEÑO MACLEAN
// <EMAIL> con contraseña Ooomy2808.

export function isMainAdmin(email, password) {
  // SOLO EL DUEÑO TIENE ACCESO DE ADMINISTRADOR
  return (
    email && 
    password && 
    email.toLowerCase() === '<EMAIL>' && 
    password === 'Ooomy2808.'
  )
}

export function verifyAdminAccess(req, res, next) {
  const { email, password } = req.body || req.query
  
  if (!isMainAdmin(email, password)) {
    return res.status(403).json({
      success: false,
      error: 'ACCESO DENEGADO: Solo el administrador principal puede realizar esta acción.',
      message: 'No tienes permisos de administrador'
    })
  }
  
  // Si es el admin principal, continuar
  if (next) next()
  return true
}

export async function requireAdminAuth(req, res) {
  const { authorization } = req.headers
  let { email, password } = req.body || {}

  // Para métodos DELETE, las credenciales pueden venir en query params
  if (req.method === 'DELETE' && req.query) {
    email = email || req.query.email
    password = password || req.query.password
  }

  // Verificar credenciales directas
  if (email && password) {
    if (isMainAdmin(email, password)) {
      return { success: true, isAdmin: true }
    }
  }

  // Verificar token de autorización si existe
  if (authorization) {
    try {
      const token = authorization.replace('Bearer ', '')
      const decoded = JSON.parse(Buffer.from(token, 'base64').toString())

      if (isMainAdmin(decoded.email, decoded.password)) {
        return { success: true, isAdmin: true }
      }
    } catch (error) {
      // Token inválido
    }
  }

  return {
    success: false,
    error: 'ACCESO DENEGADO: Solo <EMAIL> puede realizar esta acción.',
    statusCode: 403
  }
}
