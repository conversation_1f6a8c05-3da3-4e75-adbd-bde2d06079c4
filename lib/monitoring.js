// SISTEMA DE MONITOREO PARA PRODUCCIÓN - MACLEAN
import { getDatabase } from './mongodb.js'

// LOGS CRÍTICOS PARA PRODUCCIÓN
export function logCritical(message, error = null) {
  const timestamp = new Date().toISOString()
  const logEntry = {
    timestamp,
    level: 'CRITICAL',
    message,
    error: error?.message || null,
    stack: error?.stack || null,
    environment: process.env.NODE_ENV || 'unknown'
  }
  
  console.error('🚨 CRITICAL ERROR:', JSON.stringify(logEntry, null, 2))
  
  // En producción, enviar a servicio de monitoreo (Sentry, LogRocket, etc.)
  if (process.env.NODE_ENV === 'production') {
    // TODO: Integrar con servicio de monitoreo
    // Sentry.captureException(error)
  }
}

export function logInfo(message, data = null) {
  const timestamp = new Date().toISOString()
  console.log(`ℹ️ [${timestamp}] ${message}`, data ? JSON.stringify(data) : '')
}

// HEALTH CHECK PARA PRODUCCIÓN
export async function healthCheck() {
  const checks = {
    timestamp: new Date().toISOString(),
    database: false,
    environment: process.env.NODE_ENV,
    mongodb_uri_configured: !!process.env.MONGODB_URI,
    session_secret_configured: !!process.env.SESSION_SECRET
  }
  
  try {
    // Verificar conexión a base de datos
    const db = await getDatabase()
    await db.admin().ping()
    checks.database = true
    
    // Verificar que existan usuarios
    const users = db.collection('users')
    const userCount = await users.countDocuments()
    checks.user_count = userCount
    checks.users_exist = userCount > 0
    
    logInfo('Health check passed', checks)
    return { success: true, checks }
    
  } catch (error) {
    checks.database_error = error.message
    logCritical('Health check failed', error)
    return { success: false, checks, error: error.message }
  }
}

// VALIDAR CONFIGURACIÓN AL INICIO
export function validateProductionConfig() {
  const requiredEnvVars = [
    'MONGODB_URI',
    'SESSION_SECRET'
  ]
  
  const missing = requiredEnvVars.filter(varName => !process.env[varName])
  
  if (missing.length > 0) {
    const error = new Error(`Missing required environment variables: ${missing.join(', ')}`)
    logCritical('Configuration validation failed', error)
    throw error
  }
  
  // Validar URI de MongoDB
  if (!process.env.MONGODB_URI.includes('senalesmaclean.wckyrop.mongodb.net')) {
    const error = new Error('Invalid MongoDB URI - must connect to senalesmaclean cluster')
    logCritical('MongoDB URI validation failed', error)
    throw error
  }
  
  logInfo('Production configuration validated successfully')
  return true
}
