// Script para revisar usuarios - MACLEAN
import { MongoClient } from 'mongodb'

const uri = 'mongodb+srv://macleanjhon8:<EMAIL>/?retryWrites=true&w=majority&appName=SenalesMaclean'

async function checkUsers() {
  let client
  
  try {
    console.log('🚀 Conectando a MongoDB...')
    client = new MongoClient(uri)
    await client.connect()
    
    const db = client.db()
    const users = db.collection('users')
    
    console.log('🔍 Listando todos los usuarios...')
    
    const allUsers = await users.find({}).toArray()
    
    if (allUsers.length === 0) {
      console.log('❌ No se encontraron usuarios')
      return
    }
    
    console.log(`✅ Se encontraron ${allUsers.length} usuarios:`)
    console.log('=' .repeat(80))
    
    allUsers.forEach((user, index) => {
      console.log(`\n👤 Usuario ${index + 1}:`)
      console.log('   ID:', user._id.toString())
      console.log('   Nombre:', user.name || 'N/A')
      console.log('   Email:', user.email || 'N/A')
      console.log('   Teléfono:', user.phone || 'N/A')
      console.log('   Registrado:', user.registeredAt ? new Date(user.registeredAt).toLocaleDateString() : 'N/A')
      console.log('   Pagado:', user.isPaid ? 'SÍ' : 'NO')
      console.log('   Acceso:', user.hasAccess ? 'SÍ' : 'NO')
      console.log('   Activo:', user.isActive ? 'SÍ' : 'NO')
      console.log('   Días restantes:', user.daysRemaining || 0)
      console.log('   Inicio membresía:', user.membershipStartDate ? new Date(user.membershipStartDate).toLocaleDateString() : 'N/A')
      console.log('   Fin membresía:', user.membershipEndDate ? new Date(user.membershipEndDate).toLocaleDateString() : 'N/A')
      console.log('   Estado membresía:', user.membershipStatus || 'N/A')
    })
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    if (client) {
      await client.close()
      console.log('\n🔌 Conexión cerrada')
    }
  }
}

checkUsers()
