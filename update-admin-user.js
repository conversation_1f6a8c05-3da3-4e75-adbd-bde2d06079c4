// Script para actualizar el usuario administrador - MACLEAN
import { MongoClient } from 'mongodb'

const uri = process.env.MONGODB_URI || 'mongodb+srv://macleanjhon8:<EMAIL>/germayori-signals?retryWrites=true&w=majority&appName=SenalesMaclean'

async function updateAdminUser() {
  let client
  
  try {
    console.log('🚀 Conectando a MongoDB...')
    client = new MongoClient(uri)
    await client.connect()
    
    const db = client.db('germayori-signals')
    const users = db.collection('users')
    
    console.log('🔍 Actualizando usuario administrador...')
    
    // Actualizar el usuario administrador principal
    const result = await users.updateOne(
      { email: '<EMAIL>' },
      {
        $set: {
          // Asegurar que la contraseña sea correcta
          password: 'Ooomy2808.',
          // Permisos de administrador
          isAdmin: true,
          canCreateSignals: true,
          canManageUsers: true,
          canDeleteSignals: true,
          role: 'MAIN_ADMIN',
          adminLevel: 'OWNER',
          // Asegurar acceso completo
          isPaid: true,
          hasAccess: true,
          isActive: true,
          // Fechas actualizadas
          updatedAt: new Date(),
          lastAdminUpdate: new Date()
        }
      }
    )
    
    if (result.modifiedCount > 0) {
      console.log('✅ Usuario administrador actualizado exitosamente!')
      
      // Mostrar usuario actualizado
      const updatedUser = await users.findOne({ email: '<EMAIL>' })
      console.log('\n👑 ADMINISTRADOR PRINCIPAL:')
      console.log('   Email:', updatedUser.email)
      console.log('   Nombre:', updatedUser.name)
      console.log('   Contraseña:', updatedUser.password)
      console.log('   Es Admin:', updatedUser.isAdmin ? 'SÍ' : 'NO')
      console.log('   Puede crear señales:', updatedUser.canCreateSignals ? 'SÍ' : 'NO')
      console.log('   Puede gestionar usuarios:', updatedUser.canManageUsers ? 'SÍ' : 'NO')
      console.log('   Rol:', updatedUser.role)
      console.log('   Nivel:', updatedUser.adminLevel)
      console.log('   Estado:', updatedUser.isActive ? 'ACTIVO' : 'INACTIVO')
      console.log('   Acceso:', updatedUser.hasAccess ? 'SÍ' : 'NO')
      
      console.log('\n🎯 AHORA PUEDES:')
      console.log('   ✅ Iniciar sesió<NAME_EMAIL>')
      console.log('   ✅ Contraseña: Ooomy2808.')
      console.log('   ✅ Crear señales de trading')
      console.log('   ✅ Eliminar señales')
      console.log('   ✅ Ver todos los usuarios')
      console.log('   ✅ Control total de la aplicación')
      
    } else {
      console.log('⚠️ No se encontró el usuario o no se pudo actualizar')
      
      // Verificar si el usuario existe
      const existingUser = await users.findOne({ email: '<EMAIL>' })
      if (!existingUser) {
        console.log('❌ <NAME_EMAIL> no existe en la base de datos')
        console.log('📝 Creando usuario administrador...')
        
        const createResult = await users.insertOne({
          name: 'Maclean Admin',
          email: '<EMAIL>',
          password: 'Ooomy2808.',
          phone: 'Admin',
          // Permisos de administrador
          isAdmin: true,
          canCreateSignals: true,
          canManageUsers: true,
          canDeleteSignals: true,
          role: 'MAIN_ADMIN',
          adminLevel: 'OWNER',
          // Acceso completo
          isPaid: true,
          hasAccess: true,
          isActive: true,
          // Fechas
          createdAt: new Date(),
          registeredAt: new Date(),
          membershipStartDate: new Date(),
          membershipEndDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 año
          membershipExpirationDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
          daysRemaining: 365,
          membershipStatus: 'ACTIVE'
        })
        
        if (createResult.insertedId) {
          console.log('✅ Usuario administrador creado exitosamente!')
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    if (client) {
      await client.close()
      console.log('\n🔌 Conexión cerrada')
    }
  }
}

updateAdminUser()
