// Script para crear usuario de prueba - MACLEAN
import { createUser } from './lib/mongodb.js'

async function createTestUser() {
  try {
    console.log('🚀 Creando usuario de prueba...')
    
    const result = await createUser({
      name: '<PERSON><PERSON><PERSON> Prueba',
      email: '<EMAIL>',
      phone: '+507 6000-0000',
      password: '123456',
      registeredAt: new Date(),
      source: 'test-script',
      isPaid: true,
      hasAccess: true,
      paidAt: new Date()
    })

    if (result.success) {
      console.log('✅ Usuario de prueba creado exitosamente!')
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Contraseña: 123456')
      console.log('💰 Estado: PAGADO y CON ACCESO')
      console.log('🆔 ID:', result.userId)
    } else {
      console.error('❌ Error:', result.error)
    }
  } catch (error) {
    console.error('❌ Error creando usuario:', error.message)
  }
  
  process.exit(0)
}

createTestUser()
