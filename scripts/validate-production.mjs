#!/usr/bin/env node
// SCRIPT DE VALIDACIÓN ANTES DE DEPLOY A PRODUCCIÓN - MACLEAN

import { validateProductionConfig, healthCheck } from '../lib/monitoring.js'

async function validateForProduction() {
  console.log('🔍 Validando configuración para producción...\n')
  
  try {
    // 1. Validar variables de entorno
    console.log('1️⃣ Validando variables de entorno...')
    validateProductionConfig()
    console.log('✅ Variables de entorno válidas\n')
    
    // 2. Ejecutar health check
    console.log('2️⃣ Ejecutando health check...')
    const healthResult = await healthCheck()
    
    if (healthResult.success) {
      console.log('✅ Health check exitoso')
      console.log(`   📊 Usuarios en base de datos: ${healthResult.checks.user_count}`)
      console.log(`   🗄️ Base de datos conectada: ${healthResult.checks.database}`)
      console.log(`   🌍 Entorno: ${healthResult.checks.environment}\n`)
    } else {
      throw new Error(`Health check falló: ${healthResult.error}`)
    }
    
    // 3. Validaciones específicas de producción
    console.log('3️⃣ Validaciones específicas de producción...')
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('⚠️  NODE_ENV no está configurado como "production"')
    }
    
    if (process.env.MONGODB_URI.includes('localhost')) {
      throw new Error('❌ MongoDB URI apunta a localhost - debe ser la base de datos de producción')
    }
    
    if (process.env.SESSION_SECRET === 'germayori-trading-secret-2024') {
      console.log('⚠️  Usando SESSION_SECRET por defecto - cambiar en producción')
    }
    
    console.log('✅ Validaciones de producción completadas\n')
    
    // 4. Resumen final
    console.log('🎉 VALIDACIÓN EXITOSA - LISTO PARA PRODUCCIÓN')
    console.log('=' .repeat(50))
    console.log('✅ Configuración válida')
    console.log('✅ Base de datos conectada')
    console.log('✅ Usuarios existentes en base de datos')
    console.log('✅ APIs funcionando correctamente')
    console.log('\n🚀 Puedes proceder con el deploy a producción')
    
  } catch (error) {
    console.error('\n❌ VALIDACIÓN FALLÓ - NO DEPLOYAR')
    console.error('=' .repeat(50))
    console.error('Error:', error.message)
    console.error('\n🛑 Corrige los errores antes de deployar a producción')
    process.exit(1)
  }
}

// Ejecutar validación
validateForProduction()
