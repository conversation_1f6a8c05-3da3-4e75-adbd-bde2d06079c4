@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap');

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Montserrat', sans-serif;
}

body {
  color: white;
  background: linear-gradient(135deg, #111, #000);
}

a {
  color: inherit;
  text-decoration: none;
}

.font-montserrat {
  font-family: 'Montserrat', sans-serif;
}

/* Animaciones personalizadas */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* Efectos de hover personalizados */
.hover-glow:hover {
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
  transition: box-shadow 0.3s ease;
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #FFD700;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #FF4500;
}
