# VARIABLES DE ENTORNO REQUERIDAS PARA SEÑALES MACLEAN
# COPIAR ESTE ARCHIVO COMO .env.local Y COMPLETAR LOS VALORES

# ========================================
# CONFIGURACIÓN DE BASE DE DATOS (CRÍTICO)
# ========================================
MONGODB_URI=mongodb+srv://macleanjhon8:<EMAIL>/?retryWrites=true&w=majority&appName=SenalesMaclean

# ========================================
# CONFIGURACIÓN DE SEGURIDAD (CRÍTICO)
# ========================================
SESSION_SECRET=tu-clave-secreta-super-segura-aqui

# ========================================
# CONFIGURACIÓN DE NEXT.JS
# ========================================
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=tu-clave-secreta-super-segura-aqui

# ========================================
# CONFIGURACIÓN DE ENTORNO
# ========================================
NODE_ENV=development

# ========================================
# INSTRUCCIONES PARA PRODUCCIÓN
# ========================================
# 1. En Vercel/Netlify, configurar estas variables en el panel de control
# 2. NUNCA subir archivos .env* a GitHub
# 3. Usar valores diferentes para desarrollo y producción
# 4. Verificar que la URL de MongoDB sea correcta
# 5. Usar contraseñas fuertes en producción
