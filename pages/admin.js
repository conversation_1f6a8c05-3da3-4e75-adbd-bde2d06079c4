// PANEL DE ADMINISTRACIÓN - GERMAYORI
import { useState, useEffect } from 'react'
import Head from 'next/head'

export default function AdminPanel() {
  const [signals, setSignals] = useState([])
  const [users, setUsers] = useState([])
  const [stats, setStats] = useState({})
  const [showAddSignal, setShowAddSignal] = useState(false)
  const [newSignal, setNewSignal] = useState({
    pair: '',
    action: 'BUY',
    entry: '',
    takeProfit1: '',
    takeProfit2: '',
    takeProfit3: '',
    stopLoss: '',
    timeframe: '1 hora',
    status: 'ACTIVA',
    analysis: '',
    strategy: 'Aná<PERSON><PERSON> Técnico'
  })

  // Cargar datos al iniciar
  useEffect(() => {
    loadSignals()
    loadUsers()
  }, [])

  const loadSignals = async () => {
    try {
      const response = await fetch('/api/signals')
      const data = await response.json()
      if (data.success) {
        setSignals(data.signals)
      }
    } catch (error) {
      console.error('Error loading signals:', error)
    }
  }

  const loadUsers = async () => {
    try {
      const response = await fetch('/api/admin/users')
      const data = await response.json()
      if (data.success) {
        setUsers(data.users)
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Error loading users:', error)
    }
  }

  const handleAddSignal = async (e) => {
    e.preventDefault()
    try {
      const response = await fetch('/api/signals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSignal),
      })

      const data = await response.json()
      if (data.success) {
        alert('Señal agregada exitosamente!')
        setShowAddSignal(false)
        setNewSignal({
          pair: '',
          action: 'BUY',
          entry: '',
          takeProfit1: '',
          takeProfit2: '',
          takeProfit3: '',
          stopLoss: '',
          timeframe: '1 hora',
          status: 'ACTIVA',
          analysis: '',
          strategy: 'Análisis Técnico'
        })
        loadSignals()
      } else {
        alert('Error al agregar señal: ' + data.error)
      }
    } catch (error) {
      console.error('Error adding signal:', error)
      alert('Error al agregar señal')
    }
  }

  const updateSignalStatus = async (signalId, newStatus) => {
    try {
      const response = await fetch('/api/signals', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: signalId,
          status: newStatus
        }),
      })

      const data = await response.json()
      if (data.success) {
        alert('Estado actualizado!')
        loadSignals()
      }
    } catch (error) {
      console.error('Error updating signal:', error)
    }
  }

  const deleteSignal = async (signalId) => {
    if (!confirm('¿Estás seguro que deseas eliminar esta señal? Esta acción no se puede deshacer.')) {
      return
    }

    try {
      const response = await fetch(`/api/signals?id=${signalId}`, {
        method: 'DELETE',
      })

      const data = await response.json()
      if (data.success) {
        alert('Señal eliminada exitosamente!')
        loadSignals()
      } else {
        alert('Error al eliminar señal: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting signal:', error)
      alert('Error al eliminar señal')
    }
  }

  return (
    <>
      <Head>
        <title>Panel Admin - Maclean Signals</title>
      </Head>

      <div className="min-h-screen bg-gray-900 text-white p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold mb-8 text-center">
            🎯 Panel de Administración - MACLEAN
          </h1>

          {/* Estadísticas */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-green-600 p-6 rounded-lg text-center">
              <h3 className="text-2xl font-bold">{stats.totalUsers || 0}</h3>
              <p>Total Usuarios</p>
            </div>
            <div className="bg-blue-600 p-6 rounded-lg text-center">
              <h3 className="text-2xl font-bold">{stats.paidUsers || 0}</h3>
              <p>Usuarios Pagados</p>
            </div>
            <div className="bg-yellow-600 p-6 rounded-lg text-center">
              <h3 className="text-2xl font-bold">{signals.length}</h3>
              <p>Señales Activas</p>
            </div>
            <div className="bg-purple-600 p-6 rounded-lg text-center">
              <h3 className="text-2xl font-bold">{stats.totalRevenue || '$0'}</h3>
              <p>Ingresos Totales</p>
            </div>
          </div>

          {/* Botón Agregar Señal */}
          <div className="mb-6">
            <button
              onClick={() => setShowAddSignal(!showAddSignal)}
              className="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-lg font-bold"
            >
              ➕ Agregar Nueva Señal
            </button>
          </div>

          {/* Formulario Agregar Señal */}
          {showAddSignal && (
            <div className="bg-gray-800 p-6 rounded-lg mb-8">
              <h2 className="text-xl font-bold mb-4">Nueva Señal de Trading</h2>
              <form onSubmit={handleAddSignal} className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <input
                  type="text"
                  placeholder="Par (ej: EUR/USD)"
                  value={newSignal.pair}
                  onChange={(e) => setNewSignal({...newSignal, pair: e.target.value})}
                  className="bg-gray-700 p-3 rounded text-white"
                  required
                />
                <select
                  value={newSignal.action}
                  onChange={(e) => setNewSignal({...newSignal, action: e.target.value})}
                  className="bg-gray-700 p-3 rounded text-white"
                >
                  <option value="BUY">BUY</option>
                  <option value="SELL">SELL</option>
                </select>
                <input
                  type="number"
                  step="0.00001"
                  placeholder="Entrada"
                  value={newSignal.entry}
                  onChange={(e) => setNewSignal({...newSignal, entry: e.target.value})}
                  className="bg-gray-700 p-3 rounded text-white"
                  required
                />
                <input
                  type="number"
                  step="0.00001"
                  placeholder="TP1 (Requerido)"
                  value={newSignal.takeProfit1}
                  onChange={(e) => setNewSignal({...newSignal, takeProfit1: e.target.value})}
                  className="bg-gray-700 p-3 rounded text-white"
                  required
                />
                <input
                  type="number"
                  step="0.00001"
                  placeholder="TP2 (Opcional)"
                  value={newSignal.takeProfit2}
                  onChange={(e) => setNewSignal({...newSignal, takeProfit2: e.target.value})}
                  className="bg-gray-700 p-3 rounded text-white"
                />
                <input
                  type="number"
                  step="0.00001"
                  placeholder="TP3 (Opcional)"
                  value={newSignal.takeProfit3}
                  onChange={(e) => setNewSignal({...newSignal, takeProfit3: e.target.value})}
                  className="bg-gray-700 p-3 rounded text-white"
                />
                <input
                  type="number"
                  step="0.00001"
                  placeholder="Stop Loss"
                  value={newSignal.stopLoss}
                  onChange={(e) => setNewSignal({...newSignal, stopLoss: e.target.value})}
                  className="bg-gray-700 p-3 rounded text-white"
                  required
                />
                <input
                  type="text"
                  placeholder="Tiempo (ej: 2 horas)"
                  value={newSignal.timeframe}
                  onChange={(e) => setNewSignal({...newSignal, timeframe: e.target.value})}
                  className="bg-gray-700 p-3 rounded text-white"
                />
                <textarea
                  placeholder="Análisis técnico..."
                  value={newSignal.analysis}
                  onChange={(e) => setNewSignal({...newSignal, analysis: e.target.value})}
                  className="bg-gray-700 p-3 rounded text-white md:col-span-3"
                  rows="3"
                />
                <button
                  type="submit"
                  className="bg-green-600 hover:bg-green-700 p-3 rounded font-bold md:col-span-3"
                >
                  ✅ Crear Señal
                </button>
              </form>
            </div>
          )}

          {/* Lista de Señales */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4">Señales Actuales</h2>
            <div className="grid gap-4">
              {signals.map((signal) => (
                <div key={signal._id} className="bg-gray-700 p-4 rounded-lg">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-bold text-lg">{signal.pair} - {signal.action}</h3>
                      <p>
                        Entrada: {signal.entry} |
                        TP1: {signal.takeProfit1 || signal.takeProfit}
                        {signal.takeProfit2 && ` | TP2: ${signal.takeProfit2}`}
                        {signal.takeProfit3 && ` | TP3: ${signal.takeProfit3}`}
                        | SL: {signal.stopLoss}
                      </p>
                      <p className="text-sm text-gray-300">{signal.analysis}</p>
                    </div>
                    <div className="flex gap-2">
                      <span className={`px-3 py-1 rounded text-sm ${
                        signal.status === 'ACTIVA' ? 'bg-green-600' : 'bg-red-600'
                      }`}>
                        {signal.status}
                      </span>
                      <button
                        onClick={() => updateSignalStatus(signal._id,
                          signal.status === 'ACTIVA' ? 'CERRADA' : 'ACTIVA'
                        )}
                        className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm"
                      >
                        Cambiar Estado
                      </button>
                      <button
                        onClick={() => deleteSignal(signal._id)}
                        className="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm"
                      >
                        🗑️ Eliminar
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
