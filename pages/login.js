import Head from 'next/head'
import { useState } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'

export default function Login() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    const { email, password } = formData
    
    // Limpiar mensajes
    setError('')
    setSuccess('')
    
    // Validar campos
    if (!email || !password) {
      setError('Por favor completa todos los campos')
      return
    }
    
    setIsLoading(true)
    
    try {
      // Login real con MongoDB
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (data.success) {
        // Guardar sesión
        localStorage.setItem('userSession', JSON.stringify({
          userId: data.userId,
          email: data.email,
          name: data.name,
          hasAccess: data.hasAccess,
          isPaid: data.isPaid,
          isAdmin: data.isAdmin,
          canCreateSignals: data.canCreateSignals,
          canManageUsers: data.canManageUsers
        }))

        setSuccess('¡Bienvenido de vuelta! Accediendo a tus señales...')

        // Redirigir directamente a las señales
        setTimeout(() => {
          router.push('/signals')
        }, 1000)
      } else {
        if (data.needsPayment) {
          setError(data.error)
          setTimeout(() => {
            router.push('/#signup')
          }, 3000)
        } else {
          setError(data.error)
        }
      }
      
    } catch (error) {
      console.error('Error:', error)
      setError('Error al iniciar sesión. Intenta nuevamente.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <Head>
        <title>Iniciar Sesión - Señales Maclean</title>
        <meta name="description" content="Accede a tus señales premium de trading" />
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black flex items-center justify-center p-4 font-montserrat">
        <div className="bg-gray-800 p-10 rounded-3xl border-2 border-yellow-400 shadow-2xl w-full max-w-md">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-yellow-400 mb-2">🚀 Iniciar Sesión</h1>
            <p className="text-gray-400">Accede a tus señales premium</p>
          </div>
          
          {error && (
            <div className="bg-red-500 text-white p-3 rounded-lg mb-6 text-center">
              {error}
            </div>
          )}
          
          {success && (
            <div className="bg-green-500 text-white p-3 rounded-lg mb-6 text-center">
              {success}
            </div>
          )}
          
          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <label className="block text-yellow-400 font-bold mb-2">
                📧 Correo electrónico
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full p-4 rounded-xl border-2 border-gray-600 bg-gray-700 text-white focus:border-yellow-400 focus:outline-none transition-colors"
                placeholder="<EMAIL>"
                required
              />
            </div>
            
            <div className="mb-6">
              <label className="block text-yellow-400 font-bold mb-2">
                🔒 Contraseña
              </label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className="w-full p-4 rounded-xl border-2 border-gray-600 bg-gray-700 text-white focus:border-yellow-400 focus:outline-none transition-colors"
                placeholder="Tu contraseña"
                required
              />
            </div>
            
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-orange-500 hover:bg-orange-600 text-white font-bold py-4 px-6 rounded-xl text-lg transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isLoading ? '⏳ Iniciando sesión...' : '🔑 Iniciar Sesión'}
            </button>
          </form>
          
          <div className="text-center mt-6 space-y-2">
            <p className="text-gray-400">
              ¿No tienes cuenta?{' '}
              <Link href="/#signup" className="text-yellow-400 hover:text-orange-500 font-bold">
                Regístrate aquí
              </Link>
            </p>
            <p>
              <Link href="/" className="text-yellow-400 hover:text-orange-500">
                ← Volver al inicio
              </Link>
            </p>
          </div>
        </div>
      </div>
    </>
  )
}
