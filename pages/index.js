import Head from 'next/head'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'

export default function Home() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: ''
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const processPayment = async () => {
    const { name, email, phone, password } = formData
    
    // Validar campos
    if (!name || !email || !phone || !password) {
      alert('❌ Por favor completa todos los campos')
      return
    }
    
    if (password.length < 6) {
      alert('❌ La contraseña debe tener al menos 6 caracteres')
      return
    }
    
    // Solo registrar - NO dar acceso automático
    
    setIsLoading(true)
    
    try {
      // 1. Registrar usuario en MongoDB
      const registerResponse = await fetch('/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, phone, password }),
      })

      const registerData = await registerResponse.json()

      if (!registerData.success) {
        alert('❌ Error al registrar: ' + registerData.error)
        return
      }

      // Mostrar instrucciones - NO activar pago automáticamente
      alert(
        '🎉 ¡REGISTRO COMPLETADO!\\n\\n' +
        '📱 PASOS OBLIGATORIOS:\\n' +
        '1. Paga $25 USD con Yappy (QR arriba)\\n' +
        '2. Únete al grupo VIP de WhatsApp\\n' +
        '3. Sube foto del comprobante en el grupo\\n' +
        '4. Maclean verificará y te dará acceso\\n\\n' +
        '⚠️ SIN PAGO REAL = SIN ACCESO'
      )

      // Limpiar formulario
      setFormData({ name: '', email: '', phone: '', password: '' })
      
    } catch (error) {
      console.error('Error:', error)
      alert('❌ Error al procesar el registro. Por favor intenta nuevamente.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <Head>
        <title>Señales de Trading Maclean - Genera $3,000+ Mensuales</title>
        <meta name="description" content="Únete a más de 500 traders que generan ingresos consistentes con nuestras señales premium. Inversión mínima $3,000 USD." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black text-white font-montserrat">
        {/* Header */}
        <header className="bg-gray-800 bg-opacity-90 backdrop-blur-sm fixed w-full top-0 z-50 border-b-2 border-yellow-400">
          <div className="max-w-6xl mx-auto px-4 py-4 flex justify-between items-center">
            <div className="text-2xl font-bold text-yellow-400">
              🚀 Señales Maclean
            </div>
            <nav className="hidden md:flex space-x-6 items-center">
              <a href="#signup" className="hover:text-yellow-400 transition-colors">Registro</a>
              <button
                onClick={() => router.push('/login')}
                className="bg-yellow-400 text-black px-4 py-2 rounded-lg font-bold hover:bg-yellow-500 transition-colors"
              >
                🔐 Iniciar Sesión
              </button>
            </nav>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => router.push('/login')}
                className="bg-yellow-400 text-black px-3 py-2 rounded-lg font-bold text-sm"
              >
                🔐 Login
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="pt-24 pb-16 px-4">
          <div className="max-w-6xl mx-auto text-center">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              💰 GENERA $3,000+ MENSUALES
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-300">
              Con nuestras señales de trading premium. Únete a más de 500 traders exitosos.
            </p>
            <div className="bg-gray-800 p-6 rounded-2xl border-2 border-yellow-400 inline-block">
              <p className="text-3xl font-bold text-yellow-400 mb-2">🎯 INVERSIÓN MÍNIMA</p>
              <p className="text-4xl font-bold">$3,000 USD</p>
              <p className="text-gray-400 mt-2">Para maximizar ganancias</p>
            </div>
          </div>
        </section>



        {/* Proceso de Acceso */}
        <section className="py-16 px-4 bg-gray-800">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl font-bold text-center mb-12 text-yellow-400">
              🔒 Proceso para Acceder a las Señales
            </h2>

            {/* Primera fila - 4 pasos */}
            <div className="grid md:grid-cols-4 gap-6 mb-6">
              <div className="bg-gray-900 p-6 rounded-2xl border-2 border-yellow-400 text-center">
                <div className="text-4xl mb-4">1️⃣</div>
                <h3 className="text-xl font-bold text-yellow-400 mb-2">Registro</h3>
                <p className="text-gray-300">Completa el formulario con tus datos</p>
              </div>

              <div className="bg-gray-900 p-6 rounded-2xl border-2 border-yellow-400 text-center">
                <div className="text-4xl mb-4">2️⃣</div>
                <h3 className="text-xl font-bold text-yellow-400 mb-2">Pago</h3>
                <p className="text-gray-300">Paga $25 USD con Yappy escaneando el QR</p>
              </div>

              <div className="bg-gray-900 p-6 rounded-2xl border-2 border-yellow-400 text-center">
                <div className="text-4xl mb-4">3️⃣</div>
                <h3 className="text-xl font-bold text-yellow-400 mb-2">Comprobante</h3>
                <p className="text-gray-300">Sube captura de pantalla del pago</p>
              </div>

              <div className="bg-gray-900 p-6 rounded-2xl border-2 border-yellow-400 text-center">
                <div className="text-4xl mb-4">4️⃣</div>
                <h3 className="text-xl font-bold text-yellow-400 mb-2">WhatsApp</h3>
                <p className="text-gray-300">Únete al grupo VIP exclusivo</p>
              </div>
            </div>

            {/* Segunda fila - Paso 5 centrado */}
            <div className="flex justify-center mb-12">
              <div className="bg-gray-900 p-6 rounded-2xl border-2 border-yellow-400 text-center max-w-sm">
                <div className="text-4xl mb-4">5️⃣</div>
                <h3 className="text-xl font-bold text-yellow-400 mb-2">Acceso</h3>
                <p className="text-gray-300">Maclean verifica y activa tu acceso</p>
              </div>
            </div>

            <div className="bg-red-900 border-2 border-red-500 p-6 rounded-2xl text-center mb-8">
              <h3 className="text-2xl font-bold text-red-400 mb-4">⚠️ IMPORTANTE</h3>
              <p className="text-red-200 text-lg">
                Las señales de trading son EXCLUSIVAS para miembros que han pagado.
                Sin pago confirmado NO hay acceso a las señales.
              </p>
            </div>
          </div>
        </section>

        {/* Registro y Pago */}
        <section className="py-16 px-4" id="signup">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl font-bold text-center mb-12 text-yellow-400">
              🎯 Acceso a Señales Premium - $25/mes
            </h2>
            
            <div className="bg-gradient-to-br from-green-800 to-green-900 p-8 rounded-2xl mb-8 text-center">
              <h3 className="text-2xl font-bold text-yellow-400 mb-4">📱 Pago con Yappy</h3>
              <p className="mb-6 text-lg">Escanea el código QR para pagar $25 USD</p>
              
              {/* QR Code - TU QR REAL - EXTRA GRANDE */}
              <div className="bg-white p-6 rounded-2xl inline-block mb-6 shadow-2xl">
                <div className="w-[500px] h-[500px] bg-white flex items-center justify-center rounded-xl border-4 border-black">
                  <img
                    src="/yappy.png"
                    alt="QR Yappy REAL - MACLEAN @Runningpip"
                    className="w-full h-full object-contain p-2"
                  />
                </div>
                <p className="mt-4 text-lg text-gray-800 font-bold">
                  📱 Escanea con Yappy - $25 USD
                </p>
                <p className="text-gray-600">
                  MACLEAN • @Runningpip
                </p>
              </div>
            </div>

            {/* ENLACE DE WHATSAPP PROMINENTE */}
            <div className="bg-green-800 p-8 rounded-2xl border-2 border-green-400 mb-8 text-center">
              <h3 className="text-2xl font-bold text-green-400 mb-4">
                📱 Grupo VIP de WhatsApp
              </h3>
              <p className="text-green-300 mb-6 text-lg">
                Después de pagar, únete al grupo para subir tu comprobante y recibir acceso:
              </p>
              <a
                href="https://chat.whatsapp.com/DJSyPHE4h06CjBMiu8LkM4"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block bg-green-600 hover:bg-green-700 text-white font-bold py-4 px-8 rounded-full text-xl transition-colors mb-4"
              >
                🚀 UNIRSE AL GRUPO VIP
              </a>
              <div className="bg-yellow-900 p-4 rounded-xl">
                <p className="text-yellow-300 font-bold">📋 PASOS:</p>
                <p className="text-yellow-200 text-sm mt-2">
                  1️⃣ Paga $25 con Yappy (QR arriba)<br/>
                  2️⃣ Únete al grupo WhatsApp<br/>
                  3️⃣ Sube foto del comprobante<br/>
                  4️⃣ Maclean te dará acceso
                </p>
              </div>
            </div>

            {/* Formulario de Registro */}
            <div className="bg-gray-800 p-8 rounded-2xl border-2 border-yellow-400">
              <h3 className="text-2xl font-bold text-yellow-400 mb-6 text-center">
                📝 Completa tu Registro
              </h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-yellow-400 font-bold mb-2">
                    👤 Nombre completo
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full p-4 rounded-xl border-2 border-gray-600 bg-gray-700 text-white focus:border-yellow-400 focus:outline-none"
                    placeholder="Ej: Juan Pérez"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-yellow-400 font-bold mb-2">
                    📧 Correo electrónico
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full p-4 rounded-xl border-2 border-gray-600 bg-gray-700 text-white focus:border-yellow-400 focus:outline-none"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-yellow-400 font-bold mb-2">
                    📱 Número de celular
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full p-4 rounded-xl border-2 border-gray-600 bg-gray-700 text-white focus:border-yellow-400 focus:outline-none"
                    placeholder="Ej: +507 6123-4567"
                    required
                  />
                </div>

                <div>
                  <label className="block text-yellow-400 font-bold mb-2">
                    🔒 Contraseña
                  </label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="w-full p-4 rounded-xl border-2 border-gray-600 bg-gray-700 text-white focus:border-yellow-400 focus:outline-none"
                    placeholder="Mínimo 6 caracteres"
                    required
                  />
                </div>
              </div>

              <div className="bg-gray-700 p-6 rounded-xl mt-6 text-center">
                <p className="text-yellow-400 font-bold text-lg">💳 Resumen del Pago</p>
                <p className="text-white mt-2">Suscripción mensual: <strong>$25 USD</strong></p>
                <p className="text-gray-400 text-sm mt-1">Acceso por 30 días • Renovación automática</p>
              </div>

              <button
                onClick={processPayment}
                disabled={isLoading}
                className="w-full mt-8 bg-orange-500 hover:bg-orange-600 text-white font-bold py-4 px-8 rounded-xl text-xl transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? '⏳ Procesando...' : '✅ Ya Pagué - Activar mi Acceso'}
              </button>

              <p className="text-center text-gray-400 text-sm mt-4">
                Al registrarte aceptas nuestros términos y condiciones
              </p>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
