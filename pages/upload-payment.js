import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'

export default function UploadPayment() {
  const router = useRouter()
  const [userId, setUserId] = useState('')
  const [paymentProof, setPaymentProof] = useState(null)
  const [isUploading, setIsUploading] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)

  useEffect(() => {
    // Obtener userId de la URL
    const { userId: urlUserId } = router.query
    if (urlUserId) {
      setUserId(urlUserId)
    } else {
      // Si no hay userId, redirigir al inicio
      router.push('/')
    }
  }, [router.query])

  const handleFileChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      // Validar que sea una imagen
      if (!file.type.startsWith('image/')) {
        alert('❌ Por favor selecciona una imagen válida (JPG, PNG, etc.)')
        return
      }
      
      // Validar tamaño (máximo 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('❌ La imagen es muy grande. Máximo 5MB permitido.')
        return
      }
      
      setPaymentProof(file)
    }
  }

  const uploadPaymentProof = async () => {
    if (!paymentProof) {
      alert('❌ Por favor selecciona una imagen del comprobante de pago')
      return
    }

    setIsUploading(true)

    try {
      const formData = new FormData()
      formData.append('paymentProof', paymentProof)
      formData.append('userId', userId)

      const response = await fetch('/api/upload-payment-proof', {
        method: 'POST',
        body: formData
      })

      const data = await response.json()

      if (data.success) {
        setShowSuccess(true)
        
        // Mostrar mensaje de éxito y luego mostrar el enlace al grupo
        setTimeout(() => {
          alert(
            '🎉 ¡COMPROBANTE SUBIDO EXITOSAMENTE!\n\n' +
            '📱 SIGUIENTE PASO:\n' +
            '1. Únete al grupo VIP de WhatsApp\n' +
            '2. Maclean verificará tu pago\n' +
            '3. Recibirás acceso a las señales\n\n' +
            '⏰ Verificación en 24 horas máximo'
          )
        }, 1000)
        
      } else {
        alert('❌ Error al subir comprobante: ' + data.error)
      }
    } catch (error) {
      console.error('Error:', error)
      alert('❌ Error de conexión. Intenta nuevamente.')
    } finally {
      setIsUploading(false)
    }
  }

  if (!userId) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">⏳</div>
          <p className="text-white text-xl">Cargando...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-8">
        
        {/* Header */}
        <header className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-yellow-400 mb-4">
            📸 Subir Comprobante de Pago
          </h1>
          <p className="text-xl text-gray-300">
            Sube tu captura de pantalla del pago de Yappy para activar tu acceso
          </p>
        </header>

        {/* Instrucciones */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="bg-blue-900 border-2 border-blue-500 rounded-2xl p-6">
            <h2 className="text-2xl font-bold text-blue-400 mb-4">📋 Instrucciones</h2>
            <div className="space-y-3 text-gray-300">
              <p>✅ <strong>Paso 1:</strong> Realiza el pago de $25 USD por Yappy</p>
              <p>✅ <strong>Paso 2:</strong> Toma captura de pantalla del pago exitoso</p>
              <p>🔄 <strong>Paso 3:</strong> Sube la imagen aquí (ACTUAL)</p>
              <p>⏳ <strong>Paso 4:</strong> Únete al grupo VIP de WhatsApp</p>
              <p>🎯 <strong>Paso 5:</strong> Maclean verificará y activará tu acceso</p>
            </div>
          </div>
        </div>

        {!showSuccess ? (
          /* Formulario de subida */
          <div className="max-w-xl mx-auto">
            <div className="bg-gray-800 border-2 border-yellow-400 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-yellow-400 mb-6 text-center">
                📱 Seleccionar Comprobante
              </h3>
              
              <div className="mb-6">
                <label className="block text-gray-300 text-sm font-bold mb-2">
                  Imagen del comprobante de pago *
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:outline-none focus:border-yellow-400"
                />
                <p className="text-gray-400 text-sm mt-2">
                  Formatos: JPG, PNG, GIF • Máximo: 5MB
                </p>
              </div>

              {paymentProof && (
                <div className="mb-6 p-4 bg-green-900 border border-green-500 rounded-xl">
                  <p className="text-green-400 font-bold">✅ Archivo seleccionado:</p>
                  <p className="text-green-300">{paymentProof.name}</p>
                  <p className="text-green-300 text-sm">
                    Tamaño: {(paymentProof.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              )}

              <button
                onClick={uploadPaymentProof}
                disabled={!paymentProof || isUploading}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold py-4 px-6 rounded-xl text-lg transition-colors"
              >
                {isUploading ? '📤 Subiendo...' : '📤 Subir Comprobante'}
              </button>
            </div>
          </div>
        ) : (
          /* Mensaje de éxito y enlace al grupo */
          <div className="max-w-xl mx-auto">
            <div className="bg-green-900 border-2 border-green-500 rounded-2xl p-8 text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h3 className="text-3xl font-bold text-green-400 mb-4">
                ¡Comprobante Subido!
              </h3>
              <p className="text-green-300 mb-6">
                Tu comprobante ha sido enviado correctamente. Ahora puedes unirte al grupo VIP.
              </p>
              
              {/* Enlace al grupo VIP */}
              <div className="bg-green-800 p-6 rounded-xl mb-6">
                <h4 className="text-xl font-bold text-green-400 mb-4">
                  📱 Grupo VIP de WhatsApp
                </h4>
                <p className="text-green-300 mb-4">
                  Únete al grupo exclusivo para recibir señales en tiempo real y análisis adicionales
                </p>
                <a
                  href="https://chat.whatsapp.com/DJSyPHE4h06CjBMiu8LkM4"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-8 rounded-full text-lg transition-colors"
                >
                  💬 Unirse al Grupo VIP
                </a>
              </div>

              <div className="bg-yellow-900 border border-yellow-500 rounded-xl p-4">
                <p className="text-yellow-400 font-bold">⏰ Próximos pasos:</p>
                <p className="text-yellow-300 text-sm mt-2">
                  • Maclean verificará tu pago en máximo 24 horas<br/>
                  • Recibirás confirmación por WhatsApp<br/>
                  • Podrás iniciar sesión y acceder a las señales
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Botón de regreso */}
        <div className="text-center mt-8">
          <button
            onClick={() => router.push('/')}
            className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded-full transition-colors"
          >
            ← Volver al Inicio
          </button>
        </div>

      </div>
    </div>
  )
}
