import Head from 'next/head'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'

export default function Signals() {
  const router = useRouter()
  const [user, setUser] = useState(null)
  const [signals, setSignals] = useState([])
  const [calculator, setCalculator] = useState({
    capital: 3000,
    months: 1,
    percentage: 5
  })
  const [showAddSignal, setShowAddSignal] = useState(false)
  const [newSignal, setNewSignal] = useState({
    pair: '',
    action: 'BUY',
    entry: '',
    takeProfit1: '',
    takeProfit2: '',
    takeProfit3: '',
    stopLoss: '',
    timeframe: '1 hora',
    status: 'ACTIVA',
    analysis: '',
    strategy: 'Análisis Técnico'
  })

  useEffect(() => {
    checkAuthentication()
    loadSignals()
  }, [])

  const checkAuthentication = () => {
    const userSession = localStorage.getItem('userSession')
    if (!userSession) {
      alert('⚠️ Debes iniciar sesión para acceder a las señales')
      router.push('/login')
      return
    }

    const user = JSON.parse(userSession)

    // Verificar si tiene acceso pagado
    if (!user.hasAccess || !user.isPaid) {
      alert('⚠️ Tu cuenta no tiene acceso activo. Por favor realiza el pago de $25.')
      router.push('/#signup')
      return
    }

    setUser(user)
  }

  const loadSignals = async () => {
    try {
      const response = await fetch('/api/signals')
      const data = await response.json()

      if (data.success) {
        setSignals(data.signals)
      } else {
        console.error('Error loading signals:', data.error)
        // Señales de ejemplo si no hay conexión
        setSignals([
          {
            _id: '1',
            pair: 'EUR/USD',
            action: 'BUY',
            entry: 1.0850,
            takeProfit1: 1.0920,
            takeProfit2: 1.0950,
            takeProfit3: 1.0980,
            stopLoss: 1.0800,
            timeframe: '2 horas',
            status: 'ACTIVA',
            analysis: 'El EUR/USD ha roto la resistencia clave en 1.0840 con volumen institucional. Esperamos continuación alcista hacia múltiples objetivos: TP1: 1.0920, TP2: 1.0950, TP3: 1.0980. Zona de invalidación en 1.0800.'
          }
        ])
      }
    } catch (error) {
      console.error('Error fetching signals:', error)
      // Señales de ejemplo en caso de error
      setSignals([
        {
          _id: '1',
          pair: 'EUR/USD',
          action: 'BUY',
          entry: 1.0850,
          takeProfit1: 1.0920,
          takeProfit2: 1.0950,
          takeProfit3: 1.0980,
          stopLoss: 1.0800,
          timeframe: '2 horas',
          status: 'ACTIVA',
          analysis: 'El EUR/USD ha roto la resistencia clave en 1.0840 con volumen institucional. Esperamos continuación alcista hacia múltiples objetivos: TP1: 1.0920, TP2: 1.0950, TP3: 1.0980. Zona de invalidación en 1.0800.'
        }
      ])
    }
  }

  const logout = () => {
    if (confirm('¿Estás seguro que deseas cerrar sesión?')) {
      localStorage.removeItem('userSession')
      router.push('/')
    }
  }

  // Verificar si el usuario es el administrador principal
  const isMainAdmin = () => {
    return user && user.email === '<EMAIL>' && user.isAdmin
  }

  // Función temporal para debug - limpiar sesión y recargar
  const debugClearSession = () => {
    localStorage.removeItem('userSession')
    alert('Sesión limpiada. Ahora inicia sesión nuevamente.')
    router.push('/login')
  }

  // Función para agregar nueva señal (SOLO PARA EL DUEÑO)
  const handleAddSignal = async (e) => {
    e.preventDefault()

    if (!isMainAdmin()) {
      alert('❌ ACCESO DENEGADO: Solo el administrador principal puede crear señales.')
      return
    }

    try {
      const response = await fetch('/api/signals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newSignal,
          email: user.email,
          password: 'Ooomy2808.' // Credenciales del admin
        }),
      })

      const data = await response.json()
      if (data.success) {
        alert('✅ Señal creada exitosamente!')
        setShowAddSignal(false)
        setNewSignal({
          pair: '',
          action: 'BUY',
          entry: '',
          takeProfit1: '',
          takeProfit2: '',
          takeProfit3: '',
          stopLoss: '',
          timeframe: '1 hora',
          status: 'ACTIVA',
          analysis: '',
          strategy: 'Análisis Técnico'
        })
        loadSignals()
      } else {
        alert('❌ Error al crear señal: ' + data.error)
      }
    } catch (error) {
      console.error('Error adding signal:', error)
      alert('❌ Error al crear señal')
    }
  }

  // Función para eliminar señal (SOLO PARA EL DUEÑO)
  const deleteSignal = async (signalId) => {
    if (!isMainAdmin()) {
      alert('❌ ACCESO DENEGADO: Solo el administrador principal puede eliminar señales.')
      return
    }

    if (!confirm('¿Estás seguro que deseas eliminar esta señal?')) {
      return
    }

    try {
      const response = await fetch(`/api/signals?id=${signalId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: user.email,
          password: 'Ooomy2808.' // Credenciales del admin
        }),
      })

      const data = await response.json()
      if (data.success) {
        alert('✅ Señal eliminada exitosamente!')
        loadSignals()
      } else {
        alert('❌ Error al eliminar señal: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting signal:', error)
      alert('❌ Error al eliminar señal')
    }
  }

  const getDaysLeft = () => {
    // Calcular días restantes (30 días desde el pago)
    if (!user || !user.isPaid) return 0
    // Por ahora mostrar 30 días, en el futuro puedes agregar fecha de expiración
    return 30
  }

  if (!user) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-white text-xl">Verificando acceso...</div>
    </div>
  }

  return (
    <>
      <Head>
        <title>Señales Premium - Área Exclusiva | Maclean</title>
        <meta name="description" content="Área exclusiva de señales de trading premium" />
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black text-white font-montserrat">
        {/* Header */}
        <header className="bg-gray-800 p-5 border-b-2 border-yellow-400 sticky top-0 z-50">
          <div className="max-w-6xl mx-auto flex justify-between items-center">
            <div className="text-2xl font-bold text-yellow-400">🚀 Señales Maclean</div>
            <div className="flex items-center space-x-4">
              <div className={`px-4 py-2 rounded-full font-bold text-sm ${getDaysLeft() <= 5 ? 'bg-orange-500' : 'bg-green-500'}`}>
                {getDaysLeft() <= 5 ? `⚠️ Expira en ${getDaysLeft()} días` : `✅ Activa - ${getDaysLeft()} días`}
              </div>
              <button
                onClick={() => window.open('https://chat.whatsapp.com/DJSyPHE4h06CjBMiu8LkM4', '_blank')}
                className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-full font-bold transition-colors"
              >
                💬 WhatsApp
              </button>

              {/* Botón temporal de debug */}
              <button
                onClick={debugClearSession}
                className="bg-red-600 hover:bg-red-700 px-3 py-2 rounded-full font-bold transition-colors text-sm"
                title="Limpiar sesión para recargar permisos de admin"
              >
                🔄 Reset
              </button>

              <button
                onClick={logout}
                className="bg-orange-500 hover:bg-orange-600 px-4 py-2 rounded-full font-bold transition-colors"
              >
                🚪 Salir
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-6xl mx-auto p-6">
          <section className="text-center mb-10">
            <h1 className="text-4xl md:text-5xl font-bold text-yellow-400 mb-4">
              🎯 Área Exclusiva de Señales
            </h1>
            <p className="text-xl text-gray-300">
              Bienvenido <span className="text-yellow-400 font-bold">{user.name}</span>, aquí tienes acceso a nuestras señales premium
            </p>

            {/* CONTROLES DE ADMINISTRADOR - SOLO PARA EL DUEÑO */}
            {isMainAdmin() && (
              <div className="mt-8 p-6 bg-red-900 border-2 border-red-500 rounded-2xl">
                <h2 className="text-2xl font-bold text-red-400 mb-4">
                  👑 PANEL DE ADMINISTRADOR - MACLEAN
                </h2>
                <p className="text-red-300 mb-4">
                  Solo tú puedes crear, editar y eliminar señales
                </p>
                <button
                  onClick={() => setShowAddSignal(true)}
                  className="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-full font-bold text-white transition-colors"
                >
                  ➕ Agregar Nueva Señal
                </button>
              </div>
            )}
          </section>

          {/* Investment Portfolio Section */}
          <section className="mb-10">
            <h2 className="text-3xl font-bold text-yellow-400 mb-6 text-center">
              💼 Tu Portafolio de Inversiones
            </h2>
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="bg-gradient-to-r from-green-600 to-green-700 p-6 rounded-2xl text-center">
                <h3 className="text-2xl font-bold mb-2">💰 Capital Inicial</h3>
                <p className="text-3xl font-bold">$3,000 USD</p>
                <p className="text-sm opacity-80">Inversión recomendada mínima</p>
              </div>
              <div className="bg-gradient-to-r from-blue-600 to-blue-700 p-6 rounded-2xl text-center">
                <h3 className="text-2xl font-bold mb-2">📈 Ganancia Estimada</h3>
                <p className="text-3xl font-bold text-yellow-400">+$150 USD</p>
                <p className="text-sm opacity-80">5% mensual promedio</p>
              </div>
              <div className="bg-gradient-to-r from-purple-600 to-purple-700 p-6 rounded-2xl text-center">
                <h3 className="text-2xl font-bold mb-2">🎯 Total Proyectado</h3>
                <p className="text-3xl font-bold text-green-400">$3,150 USD</p>
                <p className="text-sm opacity-80">Al final del mes</p>
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-2xl border-2 border-yellow-400">
              <h3 className="text-xl font-bold text-yellow-400 mb-4">📊 Recomendaciones de Inversión</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h4 className="font-bold text-green-400 mb-2">✅ Capital Conservador</h4>
                  <p className="text-sm text-gray-300">$1,000 - $2,999 USD</p>
                  <p className="text-xs text-gray-400">Ganancia estimada: 3-4% mensual</p>
                </div>
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h4 className="font-bold text-yellow-400 mb-2">🚀 Capital Óptimo</h4>
                  <p className="text-sm text-gray-300">$3,000+ USD</p>
                  <p className="text-xs text-gray-400">Ganancia estimada: 5% mensual</p>
                </div>
              </div>
              <div className="mt-4 p-4 bg-yellow-900 rounded-lg">
                <p className="text-yellow-200 text-sm">
                  💡 <strong>Consejo:</strong> Con $3,000 USD puedes seguir todas nuestras señales y obtener un 5% mensual consistente.
                  Menor capital limita las oportunidades de trading.
                </p>
              </div>
            </div>
          </section>

          {/* Calculadora de Inversiones */}
          <section className="mb-10">
            <h2 className="text-3xl font-bold text-yellow-400 mb-6 text-center">
              🧮 Calculadora de Inversiones
            </h2>
            <div className="bg-gray-800 p-6 rounded-2xl border-2 border-yellow-400">
              <div className="grid md:grid-cols-3 gap-6 mb-6">
                <div>
                  <label className="block text-yellow-400 font-bold mb-2">💰 Capital Inicial (USD)</label>
                  <input
                    type="number"
                    value={calculator.capital}
                    onChange={(e) => setCalculator({...calculator, capital: parseFloat(e.target.value) || 0})}
                    className="w-full p-3 rounded-lg bg-gray-700 text-white border-2 border-gray-600 focus:border-yellow-400"
                    min="100"
                    step="100"
                  />
                </div>
                <div>
                  <label className="block text-yellow-400 font-bold mb-2">📅 Meses</label>
                  <input
                    type="number"
                    value={calculator.months}
                    onChange={(e) => setCalculator({...calculator, months: parseInt(e.target.value) || 1})}
                    className="w-full p-3 rounded-lg bg-gray-700 text-white border-2 border-gray-600 focus:border-yellow-400"
                    min="1"
                    max="12"
                  />
                </div>
                <div>
                  <label className="block text-yellow-400 font-bold mb-2">📈 Ganancia Mensual (%)</label>
                  <select
                    value={calculator.percentage}
                    onChange={(e) => setCalculator({...calculator, percentage: parseFloat(e.target.value)})}
                    className="w-full p-3 rounded-lg bg-gray-700 text-white border-2 border-gray-600 focus:border-yellow-400"
                  >
                    <option value="3">3% (Conservador)</option>
                    <option value="4">4% (Moderado)</option>
                    <option value="5">5% (Óptimo)</option>
                  </select>
                </div>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-blue-900 p-4 rounded-lg text-center">
                  <h4 className="text-blue-300 font-bold mb-2">💵 Ganancia Total</h4>
                  <p className="text-2xl font-bold text-blue-400">
                    ${((calculator.capital * (calculator.percentage / 100)) * calculator.months).toFixed(2)}
                  </p>
                </div>
                <div className="bg-green-900 p-4 rounded-lg text-center">
                  <h4 className="text-green-300 font-bold mb-2">🎯 Capital Final</h4>
                  <p className="text-2xl font-bold text-green-400">
                    ${(calculator.capital + ((calculator.capital * (calculator.percentage / 100)) * calculator.months)).toFixed(2)}
                  </p>
                </div>
                <div className="bg-purple-900 p-4 rounded-lg text-center">
                  <h4 className="text-purple-300 font-bold mb-2">📊 ROI Total</h4>
                  <p className="text-2xl font-bold text-purple-400">
                    {(calculator.percentage * calculator.months).toFixed(1)}%
                  </p>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gray-700 rounded-lg">
                <h4 className="text-yellow-400 font-bold mb-2">📋 Resumen de Inversión:</h4>
                <p className="text-gray-300">
                  Con <span className="text-yellow-400 font-bold">${calculator.capital.toLocaleString()}</span> de capital inicial,
                  obteniendo un <span className="text-green-400 font-bold">{calculator.percentage}%</span> mensual durante{' '}
                  <span className="text-blue-400 font-bold">{calculator.months} {calculator.months === 1 ? 'mes' : 'meses'}</span>,
                  tu ganancia será de <span className="text-green-400 font-bold">
                    ${((calculator.capital * (calculator.percentage / 100)) * calculator.months).toLocaleString()}
                  </span> USD.
                </p>
              </div>
            </div>
          </section>

          {/* Signals Grid */}
          <section>
            <h2 className="text-3xl font-bold text-yellow-400 mb-6 text-center">
              🎯 Señales de Trading en Vivo - MACLEAN
            </h2>
            <div className="grid md:grid-cols-2 gap-8 mb-10">
              {signals.map((signal) => (
                <div key={signal._id} className="bg-gray-800 rounded-2xl p-6 border-2 border-yellow-400 hover:transform hover:scale-105 transition-all">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-xl font-bold text-yellow-400">
                      {signal.pair} - {signal.action === 'BUY' ? 'Ruptura Alcista' : 'Reversión Bajista'}
                    </h3>
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-bold ${
                        signal.status === 'ACTIVA' ? 'bg-green-500' : 'bg-red-500'
                      }`}>
                        {signal.status === 'ACTIVA' ? '✅ ACTIVA' : '✅ CERRADA'}
                      </span>

                      {/* BOTÓN ELIMINAR - SOLO PARA EL DUEÑO */}
                      {isMainAdmin() && (
                        <button
                          onClick={() => deleteSignal(signal._id)}
                          className="bg-red-600 hover:bg-red-700 px-3 py-1 rounded-full text-sm font-bold transition-colors"
                          title="Solo el administrador puede eliminar señales"
                        >
                          🗑️ Eliminar
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Información básica */}
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center bg-gray-700 p-3 rounded-lg">
                      <div className="text-gray-400 text-sm">Par</div>
                      <div className="font-bold text-lg">{signal.pair}</div>
                    </div>
                    <div className="text-center bg-gray-700 p-3 rounded-lg">
                      <div className="text-gray-400 text-sm">Acción</div>
                      <div className={`font-bold text-lg ${signal.action === 'BUY' ? 'text-green-400' : 'text-red-400'}`}>
                        {signal.action === 'BUY' ? '🟢' : '🔴'} {signal.action}
                      </div>
                    </div>
                    <div className="text-center bg-gray-700 p-3 rounded-lg">
                      <div className="text-gray-400 text-sm">Entrada</div>
                      <div className="font-bold text-lg">{signal.entry}</div>
                    </div>
                  </div>

                  {/* Take Profits y Stop Loss */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h4 className="text-yellow-400 font-bold mb-3 text-center">🎯 Take Profits</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center bg-gray-600 p-2 rounded">
                          <span className="text-gray-300">TP1:</span>
                          <span className="font-bold text-green-400">{signal.takeProfit1 || signal.takeProfit}</span>
                        </div>
                        {signal.takeProfit2 && (
                          <div className="flex justify-between items-center bg-gray-600 p-2 rounded">
                            <span className="text-gray-300">TP2:</span>
                            <span className="font-bold text-green-400">{signal.takeProfit2}</span>
                          </div>
                        )}
                        {signal.takeProfit3 && (
                          <div className="flex justify-between items-center bg-gray-600 p-2 rounded">
                            <span className="text-gray-300">TP3:</span>
                            <span className="font-bold text-green-400">{signal.takeProfit3}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h4 className="text-red-400 font-bold mb-3 text-center">🛑 Stop Loss & Tiempo</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center bg-gray-600 p-2 rounded">
                          <span className="text-gray-300">Stop Loss:</span>
                          <span className="font-bold text-red-400">{signal.stopLoss}</span>
                        </div>
                        <div className="flex justify-between items-center bg-gray-600 p-2 rounded">
                          <span className="text-gray-300">Tiempo:</span>
                          <span className="font-bold text-white">{signal.timeframe}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-900 p-4 rounded-lg">
                    <div className="text-yellow-400 font-bold text-lg mb-2">📝 Análisis Técnico:</div>
                    <p className="text-gray-200 leading-relaxed">
                      {signal.analysis || 'Análisis técnico detallado disponible para esta señal.'}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {signals.length === 0 && (
              <div className="text-center py-12">
                <div className="bg-gray-800 p-8 rounded-2xl border-2 border-yellow-400">
                  <h3 className="text-2xl font-bold text-yellow-400 mb-4">📡 Cargando Señales...</h3>
                  <p className="text-gray-300">
                    Las señales se actualizan en tiempo real. Si no ves señales, contacta al administrador.
                  </p>
                </div>
              </div>
            )}
          </section>

          {/* WhatsApp Section */}
          <section className="bg-gradient-to-r from-green-600 to-green-700 p-8 rounded-2xl text-center">
            <h3 className="text-2xl font-bold mb-4">📱 Grupo VIP de WhatsApp</h3>
            <p className="text-lg mb-6">
              Únete al grupo exclusivo para recibir señales en tiempo real y análisis adicionales
            </p>
            <a
              href="https://chat.whatsapp.com/DJSyPHE4h06CjBMiu8LkM4"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-green-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-colors inline-block"
            >
              💬 Unirse al Grupo VIP
            </a>
          </section>
        </main>

        {/* MODAL PARA AGREGAR SEÑAL - SOLO PARA EL DUEÑO */}
        {showAddSignal && isMainAdmin() && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto border-2 border-yellow-400">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-yellow-400">
                  ➕ Crear Nueva Señal - ADMINISTRADOR
                </h2>
                <button
                  onClick={() => setShowAddSignal(false)}
                  className="text-red-400 hover:text-red-300 text-2xl font-bold"
                >
                  ✕
                </button>
              </div>

              <form onSubmit={handleAddSignal} className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-yellow-400 font-bold mb-2">Par de Divisas</label>
                    <input
                      type="text"
                      value={newSignal.pair}
                      onChange={(e) => setNewSignal({...newSignal, pair: e.target.value})}
                      placeholder="EUR/USD"
                      className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-yellow-400"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-yellow-400 font-bold mb-2">Acción</label>
                    <select
                      value={newSignal.action}
                      onChange={(e) => setNewSignal({...newSignal, action: e.target.value})}
                      className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-yellow-400"
                    >
                      <option value="BUY">BUY (Compra)</option>
                      <option value="SELL">SELL (Venta)</option>
                    </select>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-yellow-400 font-bold mb-2">Precio de Entrada</label>
                    <input
                      type="number"
                      step="0.00001"
                      value={newSignal.entry}
                      onChange={(e) => setNewSignal({...newSignal, entry: e.target.value})}
                      placeholder="1.08500"
                      className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-yellow-400"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-yellow-400 font-bold mb-2">Stop Loss</label>
                    <input
                      type="number"
                      step="0.00001"
                      value={newSignal.stopLoss}
                      onChange={(e) => setNewSignal({...newSignal, stopLoss: e.target.value})}
                      placeholder="1.08000"
                      className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-yellow-400"
                      required
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-yellow-400 font-bold mb-2">Take Profit 1</label>
                    <input
                      type="number"
                      step="0.00001"
                      value={newSignal.takeProfit1}
                      onChange={(e) => setNewSignal({...newSignal, takeProfit1: e.target.value})}
                      placeholder="1.09200"
                      className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-yellow-400"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-yellow-400 font-bold mb-2">Take Profit 2</label>
                    <input
                      type="number"
                      step="0.00001"
                      value={newSignal.takeProfit2}
                      onChange={(e) => setNewSignal({...newSignal, takeProfit2: e.target.value})}
                      placeholder="1.09500"
                      className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-yellow-400"
                    />
                  </div>

                  <div>
                    <label className="block text-yellow-400 font-bold mb-2">Take Profit 3</label>
                    <input
                      type="number"
                      step="0.00001"
                      value={newSignal.takeProfit3}
                      onChange={(e) => setNewSignal({...newSignal, takeProfit3: e.target.value})}
                      placeholder="1.09800"
                      className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-yellow-400"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-yellow-400 font-bold mb-2">Análisis Técnico</label>
                  <textarea
                    value={newSignal.analysis}
                    onChange={(e) => setNewSignal({...newSignal, analysis: e.target.value})}
                    placeholder="Descripción del análisis técnico y fundamentales..."
                    rows="4"
                    className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-yellow-400"
                  />
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors"
                  >
                    ✅ Crear Señal
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowAddSignal(false)}
                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition-colors"
                  >
                    ❌ Cancelar
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </>
  )
}
