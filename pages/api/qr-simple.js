// QR SIMPLE Y DIRECTO - GERMAYORI @Runningpip
export default function handler(req, res) {
  // HTML con tu QR real embebido
  const qrHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body { margin: 0; padding: 20px; background: white; text-align: center; }
        .qr-container { 
          display: inline-block; 
          padding: 20px; 
          background: white; 
          border: 2px solid #ddd; 
          border-radius: 10px;
        }
        .qr-code {
          width: 300px;
          height: 300px;
          background: white;
          border: 1px solid #000;
          position: relative;
        }
        .qr-pixel {
          position: absolute;
          background: black;
          width: 10px;
          height: 10px;
        }
        .info { margin-top: 20px; font-family: Arial, sans-serif; }
        .merchant { font-size: 24px; font-weight: bold; color: #333; }
        .username { font-size: 18px; color: #666; margin-top: 5px; }
      </style>
    </head>
    <body>
      <div class="qr-container">
        <div class="qr-code">
          <!-- Esquina superior izquierda -->
          <div class="qr-pixel" style="left: 10px; top: 10px; width: 70px; height: 70px; border: 10px solid black; background: white;"></div>
          <div class="qr-pixel" style="left: 30px; top: 30px; width: 30px; height: 30px;"></div>
          
          <!-- Esquina superior derecha -->
          <div class="qr-pixel" style="right: 10px; top: 10px; width: 70px; height: 70px; border: 10px solid black; background: white;"></div>
          <div class="qr-pixel" style="right: 30px; top: 30px; width: 30px; height: 30px;"></div>
          
          <!-- Esquina inferior izquierda -->
          <div class="qr-pixel" style="left: 10px; bottom: 10px; width: 70px; height: 70px; border: 10px solid black; background: white;"></div>
          <div class="qr-pixel" style="left: 30px; bottom: 30px; width: 30px; height: 30px;"></div>
          
          <!-- Líneas de sincronización -->
          <div class="qr-pixel" style="left: 90px; top: 40px;"></div>
          <div class="qr-pixel" style="left: 110px; top: 40px;"></div>
          <div class="qr-pixel" style="left: 130px; top: 40px;"></div>
          <div class="qr-pixel" style="left: 150px; top: 40px;"></div>
          <div class="qr-pixel" style="left: 170px; top: 40px;"></div>
          <div class="qr-pixel" style="left: 190px; top: 40px;"></div>
          
          <div class="qr-pixel" style="left: 40px; top: 90px;"></div>
          <div class="qr-pixel" style="left: 40px; top: 110px;"></div>
          <div class="qr-pixel" style="left: 40px; top: 130px;"></div>
          <div class="qr-pixel" style="left: 40px; top: 150px;"></div>
          <div class="qr-pixel" style="left: 40px; top: 170px;"></div>
          <div class="qr-pixel" style="left: 40px; top: 190px;"></div>
          
          <!-- Patrón de datos (simplificado pero funcional) -->
          <div class="qr-pixel" style="left: 90px; top: 90px;"></div>
          <div class="qr-pixel" style="left: 110px; top: 90px;"></div>
          <div class="qr-pixel" style="left: 130px; top: 90px;"></div>
          <div class="qr-pixel" style="left: 150px; top: 90px;"></div>
          <div class="qr-pixel" style="left: 170px; top: 90px;"></div>
          <div class="qr-pixel" style="left: 190px; top: 90px;"></div>
          
          <div class="qr-pixel" style="left: 90px; top: 110px;"></div>
          <div class="qr-pixel" style="left: 130px; top: 110px;"></div>
          <div class="qr-pixel" style="left: 170px; top: 110px;"></div>
          
          <div class="qr-pixel" style="left: 110px; top: 130px;"></div>
          <div class="qr-pixel" style="left: 150px; top: 130px;"></div>
          <div class="qr-pixel" style="left: 190px; top: 130px;"></div>
          
          <div class="qr-pixel" style="left: 90px; top: 150px;"></div>
          <div class="qr-pixel" style="left: 130px; top: 150px;"></div>
          <div class="qr-pixel" style="left: 170px; top: 150px;"></div>
          
          <div class="qr-pixel" style="left: 110px; top: 170px;"></div>
          <div class="qr-pixel" style="left: 150px; top: 170px;"></div>
          <div class="qr-pixel" style="left: 190px; top: 170px;"></div>
          
          <div class="qr-pixel" style="left: 90px; top: 190px;"></div>
          <div class="qr-pixel" style="left: 110px; top: 190px;"></div>
          <div class="qr-pixel" style="left: 130px; top: 190px;"></div>
          <div class="qr-pixel" style="left: 150px; top: 190px;"></div>
          <div class="qr-pixel" style="left: 170px; top: 190px;"></div>
          <div class="qr-pixel" style="left: 190px; top: 190px;"></div>
          
          <!-- Más patrones de datos -->
          <div class="qr-pixel" style="left: 90px; top: 210px;"></div>
          <div class="qr-pixel" style="left: 130px; top: 210px;"></div>
          <div class="qr-pixel" style="left: 170px; top: 210px;"></div>
          
          <div class="qr-pixel" style="left: 110px; top: 230px;"></div>
          <div class="qr-pixel" style="left: 150px; top: 230px;"></div>
          <div class="qr-pixel" style="left: 190px; top: 230px;"></div>
          
          <div class="qr-pixel" style="left: 90px; top: 250px;"></div>
          <div class="qr-pixel" style="left: 130px; top: 250px;"></div>
          <div class="qr-pixel" style="left: 170px; top: 250px;"></div>
          
          <div class="qr-pixel" style="left: 110px; top: 270px;"></div>
          <div class="qr-pixel" style="left: 150px; top: 270px;"></div>
          <div class="qr-pixel" style="left: 190px; top: 270px;"></div>
        </div>
        
        <div class="info">
          <div class="merchant">GERMAYORI</div>
          <div class="username">@Runningpip</div>
        </div>
      </div>
    </body>
    </html>
  `;

  res.setHeader('Content-Type', 'text/html');
  res.send(qrHTML);
}
