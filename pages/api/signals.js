// API PARA GESTIONAR SEÑALES DE TRADING - MACLEAN
// SOLO EL DUEÑO PUEDE CREAR/EDITAR/ELIMINAR SEÑALES
import { createSignal, getAllSignals, updateSignal, deleteSignal } from '../../lib/mongodb'
import { ObjectId } from 'mongodb'
import { requireAdminAuth } from '../../lib/adminAuth'

export default async function handler(req, res) {
  try {
    switch (req.method) {
      case 'GET':
        // Obtener todas las señales
        const result = await getAllSignals()
        if (result.success) {
          res.status(200).json({
            success: true,
            signals: result.signals
          })
        } else {
          res.status(500).json({
            success: false,
            error: 'Error al obtener señales'
          })
        }
        break

      case 'POST':
        // SOLO EL DUEÑO PUEDE CREAR SEÑALES
        const adminCheck = await requireAdminAuth(req, res)
        if (!adminCheck.success) {
          return res.status(adminCheck.statusCode || 403).json({
            success: false,
            error: 'ACCESO DENEGADO: Solo <EMAIL> puede crear señales.',
            message: 'No tienes permisos de administrador'
          })
        }

        // Crear nueva señal
        const {
          pair,
          action,
          entry,
          takeProfit1,
          takeProfit2,
          takeProfit3,
          stopLoss,
          timeframe,
          status,
          analysis,
          strategy
        } = req.body

        // Validar datos requeridos
        if (!pair || !action || !entry || !takeProfit1 || !stopLoss) {
          return res.status(400).json({
            error: 'Faltan datos requeridos: par, acción, entrada, TP1, stop loss'
          })
        }

        const newSignal = await createSignal({
          pair,
          action: action.toUpperCase(),
          entry: parseFloat(entry),
          takeProfit1: parseFloat(takeProfit1),
          takeProfit2: takeProfit2 ? parseFloat(takeProfit2) : null,
          takeProfit3: takeProfit3 ? parseFloat(takeProfit3) : null,
          stopLoss: parseFloat(stopLoss),
          timeframe: timeframe || '1 hora',
          status: status || 'ACTIVA',
          analysis: analysis || '',
          strategy: strategy || 'Análisis Técnico'
        })

        if (newSignal.success) {
          res.status(201).json({
            success: true,
            message: 'Señal creada exitosamente',
            signalId: newSignal.signalId
          })
        } else {
          res.status(500).json({
            success: false,
            error: 'Error al crear señal'
          })
        }
        break

      case 'PUT':
        // SOLO EL DUEÑO PUEDE ACTUALIZAR SEÑALES
        const adminCheckUpdate = await requireAdminAuth(req, res)
        if (!adminCheckUpdate.success) {
          return res.status(adminCheckUpdate.statusCode || 403).json({
            success: false,
            error: 'ACCESO DENEGADO: Solo <EMAIL> puede actualizar señales.',
            message: 'No tienes permisos de administrador'
          })
        }

        // Actualizar señal existente
        const { id, ...updateData } = req.body

        if (!id) {
          return res.status(400).json({
            error: 'ID de señal requerido'
          })
        }

        const updateResult = await updateSignal(new ObjectId(id), updateData)
        
        if (updateResult.success) {
          res.status(200).json({
            success: true,
            message: 'Señal actualizada exitosamente'
          })
        } else {
          res.status(500).json({
            success: false,
            error: 'Error al actualizar señal'
          })
        }
        break

      case 'DELETE':
        // SOLO EL DUEÑO PUEDE ELIMINAR SEÑALES
        const adminCheckDelete = await requireAdminAuth(req, res)
        if (!adminCheckDelete.success) {
          return res.status(adminCheckDelete.statusCode || 403).json({
            success: false,
            error: 'ACCESO DENEGADO: Solo <EMAIL> puede eliminar señales.',
            message: 'No tienes permisos de administrador'
          })
        }

        // Eliminar señal
        const { id: deleteId } = req.query

        if (!deleteId) {
          return res.status(400).json({
            error: 'ID de señal requerido'
          })
        }

        const deleteResult = await deleteSignal(new ObjectId(deleteId))
        
        if (deleteResult.success) {
          res.status(200).json({
            success: true,
            message: 'Señal eliminada exitosamente'
          })
        } else {
          res.status(500).json({
            success: false,
            error: 'Error al eliminar señal'
          })
        }
        break

      default:
        res.status(405).json({ error: 'Method not allowed' })
    }

  } catch (error) {
    console.error('Error in signals API:', error)
    res.status(500).json({
      success: false,
      error: 'Error interno del servidor'
    })
  }
}
