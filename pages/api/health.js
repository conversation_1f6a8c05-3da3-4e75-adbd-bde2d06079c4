// API DE HEALTH CHECK PARA PRODUCCIÓN - MACLEAN
import { healthCheck, validateProductionConfig } from '../../lib/monitoring.js'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Validar configuración
    validateProductionConfig()
    
    // Ejecutar health check
    const result = await healthCheck()
    
    if (result.success) {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        ...result.checks
      })
    } else {
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: result.error,
        ...result.checks
      })
    }
    
  } catch (error) {
    res.status(500).json({
      status: 'critical_error',
      timestamp: new Date().toISOString(),
      error: error.message,
      environment: process.env.NODE_ENV
    })
  }
}
