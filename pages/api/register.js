// API PARA REGISTRAR USUARIOS REALES - MACLEAN
import { createUser } from '../../lib/mongodb'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { name, email, phone, password } = req.body

    // Validar datos requeridos
    if (!name || !email || !phone || !password) {
      return res.status(400).json({
        error: 'Faltan datos requeridos: nombre, email, teléfono y contraseña'
      })
    }

    // Validar contraseña
    if (password.length < 6) {
      return res.status(400).json({
        error: 'La contraseña debe tener al menos 6 caracteres'
      })
    }

    // Crear usuario en MongoDB
    const result = await createUser({
      name,
      email: email.toLowerCase(),
      phone,
      password, // En producción deberías usar bcrypt para hashear
      registeredAt: new Date(),
      source: 'landing-page'
    })

    if (result.success) {
      res.status(201).json({
        success: true,
        message: 'Usuario registrado exitosamente',
        userId: result.userId,
        nextStep: 'payment'
      })
    } else {
      console.error('Error creating user:', result.error)
      res.status(500).json({
        success: false,
        error: `Error al registrar usuario: ${result.error || 'Error desconocido'}`
      })
    }

  } catch (error) {
    console.error('Error in register API:', error)
    res.status(500).json({
      success: false,
      error: `Error interno del servidor: ${error.message}`
    })
  }
}
