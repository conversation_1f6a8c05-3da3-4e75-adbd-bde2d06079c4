// API ADMIN - VER TODOS LOS USUARIOS REALES - MACLEAN
// SOLO EL DUEÑO PUEDE VER USUARIOS
import { getAllUsers } from '../../../lib/mongodb'
import { requireAdminAuth } from '../../../lib/adminAuth'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // SOLO EL DUEÑO PUEDE VER USUARIOS
  const adminCheck = await requireAdminAuth(req, res)
  if (!adminCheck.success) {
    return res.status(adminCheck.statusCode || 403).json({
      success: false,
      error: 'ACCESO DENEGADO: Solo <EMAIL> puede ver usuarios.',
      message: 'No tienes permisos de administrador'
    })
  }

  try {
    const result = await getAllUsers()

    if (result.success) {
      // Estadísticas rápidas
      const totalUsers = result.users.length
      const paidUsers = result.users.filter(user => user.isPaid).length
      const unpaidUsers = totalUsers - paidUsers
      const totalRevenue = paidUsers * 25 // $25 por usuario

      res.status(200).json({
        success: true,
        stats: {
          totalUsers,
          paidUsers,
          unpaidUsers,
          totalRevenue: `$${totalRevenue} USD`
        },
        users: result.users.map(user => ({
          id: user._id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          isPaid: user.isPaid,
          hasAccess: user.hasAccess,
          registeredAt: user.createdAt,
          paidAt: user.paidAt || null
        }))
      })
    } else {
      console.error('Error getting users:', result.error)
      res.status(500).json({
        success: false,
        error: `Error al obtener usuarios: ${result.error || 'Error desconocido'}`
      })
    }

  } catch (error) {
    console.error('Error in admin users API:', error)
    res.status(500).json({
      success: false,
      error: `Error interno del servidor: ${error.message}`
    })
  }
}
