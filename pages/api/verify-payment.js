// API PARA VERIFICAR PAGOS REALES - MACLEAN
import { markUserAsPaid, getUserById } from '../../lib/mongodb'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { userId, paymentReference } = req.body

    if (!userId) {
      return res.status(400).json({ 
        error: 'ID de usuario requerido' 
      })
    }

    // Verificar que el usuario existe
    const userResult = await getUserById(userId)
    if (!userResult.success || !userResult.user) {
      return res.status(404).json({
        error: 'Usuario no encontrado'
      })
    }

    // Marcar como pagado (aquí podrías integrar verificación real de Yappy)
    const paymentResult = await markUserAsPaid(userId)

    if (paymentResult.success) {
      res.status(200).json({
        success: true,
        message: 'Pago verificado exitosamente',
        whatsappLink: 'https://chat.whatsapp.com/DJSyPHE4h06CjBMiu8LkM4',
        accessGranted: true
      })
    } else {
      res.status(500).json({
        success: false,
        error: 'Error al verificar pago'
      })
    }

  } catch (error) {
    console.error('Error in verify-payment API:', error)
    res.status(500).json({
      success: false,
      error: 'Error interno del servidor'
    })
  }
}
