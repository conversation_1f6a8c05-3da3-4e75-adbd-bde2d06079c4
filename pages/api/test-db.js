// API PARA PROBAR CONEXIÓN A MONGODB - MACLEAN
import clientPromise from '../../lib/mongodb'

export default async function handler(req, res) {
  try {
    // Probar conexión
    const client = await clientPromise
    const db = client.db('maclean-signals')
    
    // Probar inserción de usuario de prueba
    if (req.method === 'POST') {
      const users = db.collection('users')
      
      const testUser = {
        name: 'Usuario Prueba',
        email: '<EMAIL>',
        phone: '+507 1234-5678',
        password: 'test123',
        createdAt: new Date(),
        isPaid: false,
        hasAccess: false,
        source: 'test'
      }
      
      const result = await users.insertOne(testUser)
      
      res.status(200).json({
        success: true,
        message: 'Usuario de prueba creado exitosamente',
        userId: result.insertedId,
        database: 'maclean-signals',
        collection: 'users'
      })
    } else {
      // Solo probar conexión
      const users = db.collection('users')
      const userCount = await users.countDocuments()
      
      res.status(200).json({
        success: true,
        message: 'Conexión a MongoDB exitosa',
        database: 'maclean-signals',
        userCount: userCount,
        timestamp: new Date()
      })
    }
    
  } catch (error) {
    console.error('Error connecting to MongoDB:', error)
    res.status(500).json({
      success: false,
      error: error.message,
      details: 'No se pudo conectar a MongoDB'
    })
  }
}
