// API DE LOGIN REAL - MACLEAN
import clientPromise, { checkMembershipStatus } from '../../lib/mongodb'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { email, password } = req.body

    // Validar datos requeridos
    if (!email || !password) {
      return res.status(400).json({ 
        error: 'Email y contraseña son requeridos' 
      })
    }

    // Conectar a MongoDB
    const client = await clientPromise
    const db = client.db()
    const users = db.collection('users')

    // Buscar usuario por email
    const user = await users.findOne({ email: email.toLowerCase() })

    if (!user) {
      return res.status(401).json({
        error: 'Usuario no encontrado. Por favor regístrate primero.'
      })
    }

    // Verificar contraseña (en un sistema real usarías bcrypt)
    // Por ahora comparamos directamente
    if (user.password !== password) {
      return res.status(401).json({
        error: 'Contraseña incorrecta'
      })
    }

    // Verificar estado de membresía actualizado
    const membershipCheck = await checkMembershipStatus(email)

    if (!membershipCheck.success) {
      return res.status(500).json({
        error: 'Error verificando estado de membresía'
      })
    }

    const { membershipInfo } = membershipCheck

    // Verificar si el usuario tiene acceso activo
    if (!membershipInfo.isPaid || !membershipInfo.hasAccess || !membershipInfo.isActive) {
      return res.status(403).json({
        error: membershipInfo.membershipStatus === 'EXPIRED'
          ? `Tu membresía ha vencido. Días restantes: ${membershipInfo.daysRemaining}. Por favor renueva tu suscripción de $25.`
          : 'Tu cuenta no tiene acceso activo. Por favor realiza el pago de $25.',
        needsPayment: true,
        membershipStatus: membershipInfo.membershipStatus,
        daysRemaining: membershipInfo.daysRemaining,
        membershipEndDate: membershipInfo.membershipEndDate
      })
    }

    // Verificar si es el administrador principal (SOLO EL DUEÑO)
    const isMainAdmin = (email.toLowerCase() === '<EMAIL>' && password === 'Ooomy2808.')

    // Login exitoso
    res.status(200).json({
      success: true,
      message: 'Login exitoso',
      userId: user._id,
      email: user.email,
      name: user.name,
      hasAccess: membershipInfo.hasAccess,
      isPaid: membershipInfo.isPaid,
      isActive: membershipInfo.isActive,
      paidAt: user.paidAt,
      membershipStartDate: membershipInfo.membershipStartDate,
      membershipEndDate: membershipInfo.membershipEndDate,
      daysRemaining: membershipInfo.daysRemaining,
      membershipStatus: membershipInfo.membershipStatus,
      // SOLO EL DUEÑO TIENE ACCESO DE ADMINISTRADOR
      isAdmin: isMainAdmin,
      canCreateSignals: isMainAdmin,
      canManageUsers: isMainAdmin
    })

  } catch (error) {
    console.error('Error in login API:', error)
    res.status(500).json({
      success: false,
      error: 'Error interno del servidor'
    })
  }
}
