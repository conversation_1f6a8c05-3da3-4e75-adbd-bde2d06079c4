import { MongoClient, ObjectId } from 'mongodb'
import formidable from 'formidable'
import fs from 'fs'
import path from 'path'

// Configuración para Next.js - deshabilitar el parser por defecto
export const config = {
  api: {
    bodyParser: false,
  },
}

const MONGODB_URI = 'mongodb+srv://macleanjhon8:<EMAIL>/'
const DB_NAME = 'senalesmaclean'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Método no permitido' })
  }

  let client

  try {
    // Parsear el formulario con archivos
    const form = formidable({
      uploadDir: './public/uploads',
      keepExtensions: true,
      maxFileSize: 5 * 1024 * 1024, // 5MB máximo
    })

    // Crear directorio de uploads si no existe
    const uploadDir = './public/uploads'
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true })
    }

    const [fields, files] = await form.parse(req)
    
    const userId = Array.isArray(fields.userId) ? fields.userId[0] : fields.userId
    const paymentProofFile = Array.isArray(files.paymentProof) ? files.paymentProof[0] : files.paymentProof

    if (!userId || !paymentProofFile) {
      return res.status(400).json({ 
        success: false, 
        error: 'Faltan datos requeridos (userId o archivo)' 
      })
    }

    // Validar que el archivo sea una imagen
    if (!paymentProofFile.mimetype.startsWith('image/')) {
      // Eliminar archivo subido
      fs.unlinkSync(paymentProofFile.filepath)
      return res.status(400).json({ 
        success: false, 
        error: 'El archivo debe ser una imagen' 
      })
    }

    // Conectar a MongoDB
    client = new MongoClient(MONGODB_URI)
    await client.connect()
    const db = client.db(DB_NAME)

    // Verificar que el usuario existe
    const user = await db.collection('users').findOne({ 
      _id: new ObjectId(userId) 
    })

    if (!user) {
      // Eliminar archivo subido
      fs.unlinkSync(paymentProofFile.filepath)
      return res.status(404).json({ 
        success: false, 
        error: 'Usuario no encontrado' 
      })
    }

    // Generar nombre único para el archivo
    const fileExtension = path.extname(paymentProofFile.originalFilename || '.jpg')
    const fileName = `payment_${userId}_${Date.now()}${fileExtension}`
    const finalPath = path.join(uploadDir, fileName)

    // Mover archivo a la ubicación final
    fs.renameSync(paymentProofFile.filepath, finalPath)

    // Actualizar usuario en MongoDB con información del comprobante
    const updateResult = await db.collection('users').updateOne(
      { _id: new ObjectId(userId) },
      {
        $set: {
          paymentProof: {
            fileName: fileName,
            filePath: `/uploads/${fileName}`,
            uploadDate: new Date(),
            originalName: paymentProofFile.originalFilename,
            fileSize: paymentProofFile.size,
            mimeType: paymentProofFile.mimetype
          },
          paymentProofUploaded: true,
          paymentProofUploadDate: new Date(),
          // Marcar como pendiente de verificación
          paymentStatus: 'proof_uploaded',
          verificationStatus: 'pending'
        }
      }
    )

    if (updateResult.matchedCount === 0) {
      // Eliminar archivo si no se pudo actualizar
      fs.unlinkSync(finalPath)
      return res.status(404).json({ 
        success: false, 
        error: 'No se pudo actualizar el usuario' 
      })
    }

    // Registrar en log de actividad
    await db.collection('activity_log').insertOne({
      userId: new ObjectId(userId),
      userEmail: user.email,
      action: 'payment_proof_uploaded',
      details: {
        fileName: fileName,
        fileSize: paymentProofFile.size,
        originalName: paymentProofFile.originalFilename
      },
      timestamp: new Date(),
      ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress
    })

    res.status(200).json({
      success: true,
      message: 'Comprobante de pago subido exitosamente',
      data: {
        fileName: fileName,
        uploadDate: new Date(),
        status: 'pending_verification'
      }
    })

  } catch (error) {
    console.error('Error al subir comprobante:', error)
    
    res.status(500).json({
      success: false,
      error: 'Error interno del servidor'
    })
  } finally {
    if (client) {
      await client.close()
    }
  }
}
