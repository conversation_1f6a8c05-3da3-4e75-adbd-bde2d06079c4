// Script para explorar toda la base de datos - MACLEAN
import { MongoClient } from 'mongodb'

const uri = 'mongodb+srv://macleanjhon8:<EMAIL>/?retryWrites=true&w=majority&appName=SenalesMaclean'

async function exploreDatabase() {
  let client
  
  try {
    console.log('🚀 Conectando a MongoDB...')
    client = new MongoClient(uri)
    await client.connect()
    
    console.log('✅ Conexión exitosa!')
    
    // Listar todas las bases de datos
    console.log('\n📊 Listando todas las bases de datos...')
    const adminDb = client.db().admin()
    const databases = await adminDb.listDatabases()
    
    console.log(`🗄️ Se encontraron ${databases.databases.length} bases de datos:`)
    databases.databases.forEach(db => {
      console.log(`   - ${db.name} (${(db.sizeOnDisk / 1024 / 1024).toFixed(2)} MB)`)
    })
    
    // Explorar cada base de datos
    for (const dbInfo of databases.databases) {
      if (dbInfo.name === 'admin' || dbInfo.name === 'local' || dbInfo.name === 'config') {
        continue // Saltar bases de datos del sistema
      }
      
      console.log(`\n🔍 Explorando base de datos: ${dbInfo.name}`)
      const db = client.db(dbInfo.name)
      
      try {
        const collections = await db.listCollections().toArray()
        console.log(`   📁 Colecciones encontradas: ${collections.length}`)
        
        for (const collection of collections) {
          console.log(`\n   📋 Colección: ${collection.name}`)
          const coll = db.collection(collection.name)
          const count = await coll.countDocuments()
          console.log(`      📊 Documentos: ${count}`)
          
          if (count > 0 && count <= 10) {
            console.log(`      📄 Primeros documentos:`)
            const docs = await coll.find({}).limit(5).toArray()
            docs.forEach((doc, index) => {
              console.log(`         ${index + 1}. ID: ${doc._id}`)
              if (doc.email) console.log(`            Email: ${doc.email}`)
              if (doc.name) console.log(`            Nombre: ${doc.name}`)
              if (doc.phone) console.log(`            Teléfono: ${doc.phone}`)
              if (doc.isPaid !== undefined) console.log(`            Pagado: ${doc.isPaid}`)
              if (doc.hasAccess !== undefined) console.log(`            Acceso: ${doc.hasAccess}`)
              if (doc.isActive !== undefined) console.log(`            Activo: ${doc.isActive}`)
              if (doc.membershipEndDate) console.log(`            Vence: ${new Date(doc.membershipEndDate).toLocaleDateString()}`)
            })
          } else if (count > 10) {
            console.log(`      📄 Muestra de documentos:`)
            const docs = await coll.find({}).limit(3).toArray()
            docs.forEach((doc, index) => {
              console.log(`         ${index + 1}. ID: ${doc._id}`)
              if (doc.email) console.log(`            Email: ${doc.email}`)
              if (doc.name) console.log(`            Nombre: ${doc.name}`)
            })
          }
        }
      } catch (error) {
        console.log(`   ❌ Error explorando ${dbInfo.name}: ${error.message}`)
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    if (client) {
      await client.close()
      console.log('\n🔌 Conexión cerrada')
    }
  }
}

exploreDatabase()
